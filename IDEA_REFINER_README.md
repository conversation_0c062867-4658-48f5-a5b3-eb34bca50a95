# Idea Refiner Script

This script transforms high-level ideas into comprehensive JIRA ticket descriptions using your project's knowledge base and optional LLM assistance.

## Overview

The Idea Refiner analyzes your project's historical data to create detailed, context-aware ticket descriptions that follow your team's established patterns and conventions.

### What it analyzes:
- **Past JIRA tickets** - Learns patterns for business value, acceptance criteria, and impact assessments
- **Enriched ticket data** - Understands which files typically change for different types of work
- **Project structure** - Identifies technical components and architectural patterns
- **Git history** - Learns from commit patterns and change frequencies

### What it generates:
- **Comprehensive ticket descriptions** with business value, acceptance criteria, open questions
- **Technical design suggestions** based on project architecture
- **File change predictions** based on similar historical tickets
- **Impact assessments** and notification requirements
- **Related ticket references** for context

## Requirements

### Python Dependencies
```bash
pip install mlx-lm  # For LLM features (optional)
```

### Input Files
1. **ideas.txt** - Your high-level ideas (one per line or numbered list)
2. **jira_tickets.json** - Historical JIRA tickets
3. **enriched_tickets_clean.json** - Tickets with file change data
4. **tree.log** - Project structure (from `tree` command)
5. **gitlog.log** - Git commit history

## Usage

### Basic Usage
```bash
python3 idea_refiner.py \
    --ideas ideas.txt \
    --jira_tickets jira_tickets.json \
    --enriched_tickets enriched_tickets_clean.json \
    --tree_log tree.log \
    --git_log gitlog.log \
    --output_dir refined_tickets
```

### With LLM Model Selection
```bash
python3 idea_refiner.py \
    --ideas sample_ideas.txt \
    --jira_tickets jira_tickets.json \
    --enriched_tickets enriched_tickets_clean.json \
    --tree_log tree.log \
    --git_log gitlog.log \
    --output_dir refined_tickets \
    --model qwen3 \
    --log_level INFO
```

### Quick Test
```bash
python3 test_idea_refiner.py
```

## Available Models

The script supports three LLM models (requires MLX):

- **qwen3** - Qwen 3 30B (recommended, large context window)
- **gemma3** - Gemma 3 27B (good balance)
- **alma13** - ALMA 13B (faster, smaller)

If MLX is not available, the script falls back to template-based refinement.

## Input File Formats

### ideas.txt
Simple text file with one idea per line:
```
Add support for Apple Pay transactions
Implement real-time fraud detection using ML
Create unified dashboard for payment monitoring
```

Or numbered list:
```
1. Add support for Apple Pay transactions
2. Implement real-time fraud detection using ML
3. Create unified dashboard for payment monitoring
```

### Other Input Files
- **jira_tickets.json** - Array of ticket objects with `key`, `title`, `description`
- **enriched_tickets_clean.json** - Same as above but with `files_changed` array
- **tree.log** - Output from `tree` command showing project structure
- **gitlog.log** - Output from `git log --name-status`

## Output Format

Each idea generates a separate `.txt` file in the output directory with:

```markdown
# [Generated Title]

**Original Idea:** [Your original idea]

*Business Value:*
[Context-aware business justification]

*Acceptance Criteria:*
* [Specific, testable requirements]
* [Based on project patterns]

*Open Questions:*
* [Technical questions to resolve]
* [Business decisions needed]

*Suggested Design:*
[High-level technical approach based on project architecture]

*Impact and Notifications:*
[Risk assessment and team notification requirements]

*Likely Files to Modify:*
* [Predicted file changes based on historical data]

*Related Tickets:*
* [Similar tickets from project history]
```

## Features

### Knowledge Extraction
- **Pattern Recognition** - Learns from your ticket writing style
- **Domain Classification** - Identifies business areas (auth, clearing, fraud, etc.)
- **Component Mapping** - Maps ideas to technical components
- **File Prediction** - Suggests likely file changes based on similar tickets

### LLM Integration
- **Context-Aware Prompting** - Uses project knowledge to guide LLM
- **Fallback Support** - Works without LLM using templates
- **Model Selection** - Choose appropriate model for your needs

### Template-Based Refinement
When LLM is not available, uses intelligent templates based on:
- Historical ticket patterns
- Domain-specific knowledge
- Component relationships
- File change patterns

## Configuration

### Model Paths
Edit the `PRESETS` dictionary in `idea_refiner.py` to match your model locations:

```python
PRESETS = {
    "gemma3": "/path/to/your/gemma-3-27b-it-qat-4bit",
    "qwen3": "/path/to/your/Qwen3-30B-A3B-MLX-4bit", 
    "alma13": "/path/to/your/ALMA-13B-R-4bit-mlx",
}
```

### Logging
Control verbosity with `--log_level`:
- `DEBUG` - Detailed processing information
- `INFO` - Progress updates (default)
- `WARNING` - Warnings only
- `ERROR` - Errors only

## Examples

### Sample Ideas
The script includes `sample_ideas.txt` with example ideas:
- Apple Pay support
- Real-time fraud detection
- Unified monitoring dashboard
- Cryptocurrency payments
- Automated reconciliation

### Generated Output
Each idea becomes a comprehensive ticket like:

**Title:** "Implement real-time fraud detection using machine learning models"

**Business Value:** "Reduces fraud losses and improves customer trust. Implement real-time fraud detection using machine learning models"

**Acceptance Criteria:**
- Feature is implemented and tested
- False positive rate minimized
- Detection accuracy meets requirements
- Performance impact assessed

**Open Questions:**
- What is the acceptable false positive rate?
- How will we train and validate models?
- What data privacy considerations apply?

## Troubleshooting

### MLX Not Available
If you see "MLX not available", the script will use template-based refinement. To enable LLM features:
```bash
pip install mlx-lm
```

### Missing Input Files
Ensure all required files exist:
- `jira_tickets.json`
- `enriched_tickets_clean.json` 
- `tree.log`
- `gitlog.log`
- `ideas.txt` or `sample_ideas.txt`

### Model Loading Issues
Check that model paths in `PRESETS` are correct and models are properly downloaded.

## Integration

The generated ticket files can be:
- Copied directly into JIRA ticket descriptions
- Used as templates for team refinement sessions
- Modified and enhanced by product managers
- Integrated into your ticket creation workflow

## Performance

- **Template-based**: Very fast, processes ideas in seconds
- **LLM-based**: Slower but higher quality, depends on model size
- **Memory usage**: Varies by model (13B-30B parameters)
- **Disk space**: Models require 15-60GB storage

The script is designed to be practical for regular use while providing high-quality, context-aware ticket descriptions that save significant time in the refinement process.
