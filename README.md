# JIRA Ticket Enrichment Script

This script enriches JIRA tickets with file changes from git log data.

## Overview

The script takes two input files:
1. `jira_tickets.json` - A JSON file containing JIRA tickets with keys, titles, and descriptions
2. `gitlog.log` - A git log file showing commit history with file changes

It produces an enriched JSON output where each JIRA ticket includes a `files_changed` array showing which files were modified for that ticket.

## Usage

```bash
python3 enrich_jira_tickets.py <jira_tickets.json> <gitlog.log> > enriched_output.json
```

### Example

```bash
python3 enrich_jira_tickets.py jira_tickets.json gitlog.log > enriched_tickets.json
```

## Input File Formats

### jira_tickets.json
Expected format:
```json
[
  {
    "key": "MARS-2814",
    "title": "Migrate new terminals from already migrated merchants",
    "description": "Business value and acceptance criteria..."
  }
]
```

### gitlog.log
Expected format from `git log --name-status`:
```
commit 3907f6478df2ef39aa86ce57b265e11e2dd16f14
Author: denis.iadarinkin <<EMAIL>>
Date:   Thu Nov 28 10:19:27 2024 +0000

    [MARS-2813] added checks for duplicate de11+de7 for MC

M	internal/auth/app/authsimmc/handler.go
```

## Output Format

The script produces JSON with the same structure as the input JIRA tickets, but with an additional `files_changed` field:

```json
[
  {
    "key": "MARS-2802",
    "title": "reject amex auths with zero amount as bad request",
    "description": "...",
    "files_changed": [
      {
        "file_path": "internal/auth/app/authamex/app.go",
        "change_type": "M"
      },
      {
        "file_path": "internal/auth/internal/validation/rules_auth.go",
        "change_type": "M"
      }
    ]
  }
]
```

## Change Types

- `M` - Modified file
- `A` - Added file  
- `D` - Deleted file
- For renamed files, the script creates two entries: one `D` (delete) for the old path and one `A` (add) for the new path

## Features

- **JIRA Ticket Detection**: Automatically finds JIRA ticket references in commit messages (format: `MARS-XXXX`)
- **File Change Parsing**: Extracts file paths and change types from git log
- **Deduplication**: Removes duplicate file changes for the same ticket
- **Progress Reporting**: Shows progress and summary statistics
- **Error Handling**: Provides clear error messages for missing files or parsing issues

## Requirements

- Python 3.6+
- No external dependencies (uses only standard library)

## Generating Input Files

### Git Log
To generate the git log file in the correct format:
```bash
git log --name-status > gitlog.log
```

### JIRA Tickets
The JIRA tickets JSON should be exported from your JIRA system or created manually in the expected format.

## Summary Statistics

The script outputs summary statistics to stderr:
- Total tickets processed
- Number of tickets with file changes
- Total file changes found

## Error Handling

The script will exit with an error code and message if:
- Input files don't exist
- JSON parsing fails
- File permissions issues occur
