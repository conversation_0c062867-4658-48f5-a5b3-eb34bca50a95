{"start_time": "2025-06-20T19:12:52.482012", "script_version": "LLM-powered idea refiner v2.0", "ideas_processed": 0, "errors": [], "processing_times": {}, "model_preset": "qwen3", "ideas_file": "ideas.txt", "jira_tickets_file": "jira_tickets.json", "enriched_tickets_file": "enriched_tickets_clean.json", "tree_log_file": "tree.log", "git_log_file": "gitlog.log", "output_dir": "llm_refined_clean", "log_level": "INFO", "end_time": "2025-06-20T19:13:22.686987", "total_runtime_seconds": 30.204975}