=== PROMPT FOR IDEA 2 ===
Original Idea: Clearing instructions are to be sent via Kafka and not SQS
Timestamp: 2025-06-20T19:19:15.104032
Processing Time: 42.93 seconds
==================================================

/no_think

You are a senior technical architect for a payment processing system handling authorization and clearing for multiple card schemes (Visa, Mastercard, Amex, JCB, Diners, UnionPay).

DOMAIN KNOWLEDGE:
- Authorization: Real-time payment approval/decline decisions
- Clearing: Settlement and reconciliation of transactions
- Schemes: Each card scheme has specific rules and data formats
- ISO 8583: Standard message format for card transactions
- Data Elements (DE): Specific fields in transaction messages (DE3=processing code, DE7=datetime, etc.)
- Component Tests: Integration tests that validate end-to-end flows

SYSTEM COMPONENTS: sqs_messaging

PROJECT CONTEXT:
SIMILAR HISTORICAL TICKETS:
1. MARS-1933: dashboards are not reacting to scheme dropdown change
2. MARS-2199: Investigate Ecom transactions which are being rejected in Clearing.
3. MARS-2622: MC transactions failed to be stored

DOMAIN-SPECIFIC EXAMPLES:
- Migrate new terminals from already migrated merchants
- reduce STAN duplicates generated within same second for MC txns

COMMON FILE PATTERNS: internal/operation/operations_requests.go, internal/auth/internal/model/financial_transaction.go, internal/auth/internal/scheme/jcb/converters_events_test.go, internal/auth/internal/model/testdata/operationrequest/operation_request.go, internal/scheme/mastercard/converters_auth_responses.go

TASK: Refine this payment processing idea into a comprehensive JIRA ticket:
IDEA: "Clearing instructions are to be sent via Kafka and not SQS"

REQUIREMENTS:
1. Create specific, measurable acceptance criteria (NOT generic like 'feature implemented')
2. Include scheme-specific considerations if applicable
3. Provide concrete technical implementation details
4. Ask specific technical questions, not generic ones
5. Reference actual file paths and system components
6. Consider compliance and certification requirements

CRITICAL: Respond ONLY with valid JSON. Do not include any explanatory text, reasoning, or markdown formatting outside the JSON structure.

JSON SCHEMA REQUIRED:
{
  "title": "Concise, actionable title",
  "business_value": "Specific business impact and compliance benefits",
  "acceptance_criteria": [
    "Specific, measurable criterion with technical details",
    "Another specific criterion with scheme references",
    "Criterion with file/component references"
  ],
  "open_questions": [
    "Specific technical question about implementation",
    "Specific business question about requirements",
    "Question about compliance or certification"
  ],
  "technical_design": "Detailed technical approach with specific components, file references, and implementation steps",
  "likely_files": [
    "path/to/specific/file1.java",
    "path/to/specific/file2.yaml",
    "path/to/test/file.java"
  ],
  "impact_assessment": "Specific risks, dependencies, and notification requirements"
}

Generate JSON response for a payment processing engineer: