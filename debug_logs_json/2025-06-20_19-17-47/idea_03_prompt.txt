=== PROMPT FOR IDEA 3 ===
Original Idea: Amex wants a reversal local datetime to be exactly like the time on a terminal
Timestamp: 2025-06-20T19:19:58.691804
Processing Time: 43.59 seconds
==================================================

/no_think

You are a senior technical architect for a payment processing system handling authorization and clearing for multiple card schemes (Visa, Mastercard, Amex, JCB, Diners, UnionPay).

DOMAIN KNOWLEDGE:
- Authorization: Real-time payment approval/decline decisions
- Clearing: Settlement and reconciliation of transactions
- Schemes: Each card scheme has specific rules and data formats
- ISO 8583: Standard message format for card transactions
- Data Elements (DE): Specific fields in transaction messages (DE3=processing code, DE7=datetime, etc.)
- Component Tests: Integration tests that validate end-to-end flows

SYSTEM COMPONENTS: sqs_messaging

PROJECT CONTEXT:
SIMILAR HISTORICAL TICKETS:
1. MARS-2745: use correct reversal reason values for MC
   Files changed: cmd/auth-fetcher/auth-fetcher, internal/auth/internal/scheme/mc/converters_reversal_requests_test.go, internal/auth/internal/scheme/mc/mappers.go
2. MARS-2400: AMEX tech debt
3. MARS-2507: Local date and time in DF12 should be in the same timezone for auth and reversals 
   Files changed: componenttest/amexapi/authorize_3ds_test.go, componenttest/amexapi/capture_test.go, componenttest/amexapi/certification_test.go

DOMAIN-SPECIFIC EXAMPLES:
- Set correct amount in recon events for partial reversals
- Implement partial reversals

COMMON FILE PATTERNS: internal/auth/internal/scheme/mc/converters_reversal_requests_test.go, internal/auth/internal/scheme/mc/converters_reversal_requests.go, componenttest/amexapi/request_v2.go, go.mod, go.sum

TASK: Refine this payment processing idea into a comprehensive JIRA ticket:
IDEA: "Amex wants a reversal local datetime to be exactly like the time on a terminal"

REQUIREMENTS:
1. Create specific, measurable acceptance criteria (NOT generic like 'feature implemented')
2. Include scheme-specific considerations if applicable
3. Provide concrete technical implementation details
4. Ask specific technical questions, not generic ones
5. Reference actual file paths and system components
6. Consider compliance and certification requirements

CRITICAL: Respond ONLY with valid JSON. Do not include any explanatory text, reasoning, or markdown formatting outside the JSON structure.

JSON SCHEMA REQUIRED:
{
  "title": "Concise, actionable title",
  "business_value": "Specific business impact and compliance benefits",
  "acceptance_criteria": [
    "Specific, measurable criterion with technical details",
    "Another specific criterion with scheme references",
    "Criterion with file/component references"
  ],
  "open_questions": [
    "Specific technical question about implementation",
    "Specific business question about requirements",
    "Question about compliance or certification"
  ],
  "technical_design": "Detailed technical approach with specific components, file references, and implementation steps",
  "likely_files": [
    "path/to/specific/file1.java",
    "path/to/specific/file2.yaml",
    "path/to/test/file.java"
  ],
  "impact_assessment": "Specific risks, dependencies, and notification requirements"
}

Generate JSON response for a payment processing engineer: