{"idea_number": 4, "original_idea": "Squash all our DB migrations into a simpler big one", "timestamp": "2025-06-20T19:20:43.762104", "context": {"similar_tickets": [], "domain_examples": [{"key": "MARS-2814", "title": "Migrate new terminals from already migrated merchants", "description": "*Business Value:*\n\nNew terminals from merchants already migrated to CARBS Auth are onboarded with SV as the default processor. These should be migrated to CARBS\n\n[^F2F_All terminals_0612.csv] \n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *NO*\n** (if yes - notify AG/RAG/Clearing)\n* Does it affect resources shared with Clearing? *NO*\n** (if yes - notify Clearing)\n\n*Acceptance Criteria:*\n\n* Migrate new list of terminals"}, {"key": "MARS-2811", "title": "CARBS Terminal Migration on 05/12", "description": "*Business Value:*  To Migrate F2F terminals for VISA & Mastercard merchants on CARBS\n\n*Acceptance Criteria:*\n\nF2F terminals with required details to migrate are provided in CSV format as required. see attached\n\n[^F2F_05122024.csv]\n\n"}], "file_patterns": ["internal/database/migrations/auth_bootstrap/mc_auth/0006_init_mc_auth_db.up.sql", "internal/database/migrations/auth_bootstrap/visa_auth/0006_init_visa_auth_db.up.sql", "internal/database/migrations/migrations_test.go", "internal/database/migrations/auth_bootstrap/mc_auth/0001_init_mc_auth_db.up.sql", "internal/database/migrations/auth_bootstrap/mc_auth/0002_add_event_regen_user.up.sql", "internal/database/migrations/auth_bootstrap/mc_auth/0004_delete_legacy_app_users.up.sql", "internal/database/migrations/auth_bootstrap/mc_auth/0005_add_replication_role_and_trigger.up.sql", "internal/database/migrations/auth_bootstrap/mc_auth/0006_grant_create_to_replication_role.up.sql", "internal/database/migrations/auth_bootstrap/mc_auth/0003_add_new_app_users.up.sql", "internal/database/migrations/auth_bootstrap/visa_auth/0001_init_visa_auth_db.up.sql"], "components": ["sqs_messaging"]}}