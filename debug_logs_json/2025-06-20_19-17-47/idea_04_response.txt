=== RAW LLM RESPONSE FOR IDEA 4 ===
Original Idea: Squash all our DB migrations into a simpler big one
Timestamp: 2025-06-20T19:20:43.761967
Response Length: 10088 characters
==================================================

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just the JSON.

Do not include any markdown formatting. Ensure the JSON is valid and matches the schema. Do not add any extra fields. Do not use any special characters. Do not include any explanations. Just