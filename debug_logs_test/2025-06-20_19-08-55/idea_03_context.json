{"idea_number": 3, "original_idea": "Amex wants a reversal local datetime to be exactly like the time on a terminal", "timestamp": "2025-06-20T19:10:47.720113", "context": {"similar_tickets": [{"ticket": {"key": "MARS-2745", "title": "use correct reversal reason values for MC", "description": "{{authmodel.REVERSALREASON_NOT_COMPLETED: \"60\", // \"response received late\" in MC documentation}}\n\nwe should use value 68 (based on the spec) value 60 seems to be a mistake"}, "similarity_score": 0.16216216216216217, "file_changes": ["cmd/auth-fetcher/auth-fetcher", "internal/auth/internal/scheme/mc/converters_reversal_requests_test.go", "internal/auth/internal/scheme/mc/mappers.go", "internal/auth/internal/scheme/mc/mappers_test.go"]}, {"ticket": {"key": "MARS-2400", "title": "AMEX tech debt", "description": "The tickets in this milestone are considered to be *nice-to-haves* for amex\n\n--\n\n*Does this require a discovery/brainstorming session:  NO*\n\n*Does this require a PoC:  NO*"}, "similarity_score": 0.16129032258064516, "file_changes": []}, {"ticket": {"key": "MARS-2507", "title": "Local date and time in DF12 should be in the same timezone for auth and reversals ", "description": "DF12 for auths and reversals should be generated using UK timezone"}, "similarity_score": 0.14285714285714285, "file_changes": ["componenttest/amexapi/authorize_3ds_test.go", "componenttest/amexapi/capture_test.go", "componenttest/amexapi/certification_test.go", "internal/amex/generators.go", "internal/amex/generators_test.go", "internal/amex/request_converter.go", "internal/amex/request_converter_test.go"]}], "domain_examples": [{"key": "MARS-2807", "title": "Set correct amount in recon events for partial reversals", "description": "*Business Value:*\n\nCurrently, we set amt based on {{DE04}} [[code ref|https://github.com/dojo-engineering/mars/blob/11209a7403f9e064eefeee4a06cdf0f76b0ce61c/internal/auth/internal/scheme/mc/converters_events.go#L259-L262]]. \n\nThis doesn’t work for partial reversals, at least for MC and Visa as we set the new auth amount in DE95 [[code ref|https://github.com/dojo-engineering/mars/blob/11209a7403f9e064eefeee4a06cdf0f76b0ce61c/internal/auth/internal/scheme/mc/converters_reversal_requests.go#L89]]. \n\n*Example transaction*\n\n{noformat} {\n    \"FTID\": \"d4993be8-1044-417e-b38e-12d68d225332\",\n    \"OperationId\": \"e7b0bcf0-d50b-4b8b-b827-81e4e01bad1d\",\n    \"Scheme\": \"mc\",\n    \"Region\": \"ldn\",\n    \"Timestamp\": \"2024-11-28T12:34:08.261262Z\",\n    \"OperationRequest\": {\n      \"initiator\": \"merchant\",\n      \"reason\": \"VOIDED_BY_CARDHOLDER\",\n      \"primaryAccountNumber\": \"535674******7863\",\n      \"adjustmentAmountMinorUnits\": 69,\n      \"originalAmountMinorUnits\": 100,\n      \"gatewayOperationID\": \"ddd973dc-d29e-4886-ba9b-5d98bfd787bd\"\n    },\n    \"SchemeReq\": {\n      \"MTI\": \"0400\",\n      \"Header\": null,\n      \"Body\": {\n        \"11\": \"293200\",\n        \"12\": \"123252\",\n        \"13\": \"1128\",\n        \"14\": \"2512\",\n        \"18\": \"5812\",\n        \"2\": \"535674******7863\",\n        \"22\": \"051\",\n        \"23\": \"000\",\n        \"3\": \"000000\",\n        \"32\": \"033447\",\n        \"33\": \"201082\",\n        \"37\": \"************\",\n        \"38\": \"Tm920R\",\n        \"39\": \"32\",\n        \"4\": \"************\",\n        \"41\": \"********\",\n        \"42\": \"***************\",\n        \"43\": \"Pergola on the Wharf   London        GBR\",\n        \"48\": \" 2001S6315MDHTTCAIY1128  \",\n        \"49\": \"826\",\n        \"61\": \"0000014000300826E14 5AR\",\n        \"7\": \"1128123408\",\n        \"90\": \"010029320011281232520000003344700000201082\",\n        \"95\": \"000000000069000000000000000000000000000000\"\n      }\n    },\n    \"SchemeResp\": {\n      \"MTI\": \"0410\",\n      \"Header\": null,\n      \"Body\": {\n        \"10\": \"61000000\",\n        \"11\": \"293200\",\n        \"15\": \"1128\",\n        \"16\": \"1127\",\n        \"2\": \"535674******7863\",\n        \"3\": \"000000\",\n        \"32\": \"033447\",\n        \"33\": \"201082\",\n        \"37\": \"************\",\n        \"39\": \"00\",\n        \"4\": \"************\",\n        \"41\": \"********\",\n        \"48\": \"F6315MDHTTCAIY1128  \",\n        \"49\": \"826\",\n        \"51\": \"826\",\n        \"56\": \"0133012950018P10Q8QYJDCIZ7OZT0IK8UWDB\",\n        \"6\": \"************\",\n        \"63\": \"MDHZT1OQ4\",\n        \"7\": \"1128123408\",\n        \"90\": \"010029320011281232520000003344700000201082\",\n        \"95\": \"000000000069000000000086000000000069000000\"\n      }\n    },\n    \"Outcome\": \"SUCCESS\",\n    \"Events\": [\n      {\n        \"EventType\": \"dual_message_response\",\n        \"CreatedDate\": \"2024-11-28T12:34:08.856732Z\",\n        \"Body\": {\n          \"data\": {\n            \"acquirerBIN\": \"033447\",\n            \"financialTransactionID\": \"d4993be8-1044-417e-b38e-12d68d225332\",\n            \"gatewayOperationID\": \"ddd973dc-d29e-4886-ba9b-5d98bfd787bd\",\n            \"marketID\": \"GBR\",\n            \"merchantID\": \"***************\",\n            \"messageTypeIndicator\": \"0410\",\n            \"operationID\": \"e7b0bcf0-d50b-4b8b-b827-81e4e01bad1d\",\n            \"outcome\": \"APPROVED\",\n            \"processingCode\": \"000000\",\n            \"retrievalReferenceNumber\": \"************\",\n            \"scheme\": \"mastercard\",\n            \"schemeResponseCode\": \"00\",\n            \"schemeTransactionID\": \"MDHZT1OQ41128\",\n            \"systemTraceAuditNumber\": \"293200\",\n            \"terminalID\": \"********\",\n            \"transactionAmount\": {\n              \"currencyCode\": \"826\",\n              \"minorUnits\": 100\n            },\n            \"transactionLocalDate\": \"1128\",\n            \"transactionLocalTime\": \"123252\",\n            \"transmissionDateAndTime\": \"2024-11-28T12:34:08.856575832Z\",\n            \"upstreamReference\": \"ddd973dc-d29e-4886-ba9b-5d98bfd787bd\"\n          },\n          \"datacontenttype\": \"application/json\",\n          \"id\": \"6d2598d6-de86-4c87-9cbf-c8c8e026914e\",\n          \"source\": \"urn:tech.dojo.authmodel.auth-mastercard\",\n          \"specversion\": \"1.0\",\n          \"time\": \"2024-11-28T12:34:08.856647108Z\",\n          \"traceparent\": \"00-131101dc320efc5512f4a5dd09e3be08-4e5c09474a9491d8-01\",\n          \"type\": \"com.dojo.authmodel.DualMessageResponseV1\"\n        },\n        \"ErrMsg\": null\n      }\n    ]\n  },{noformat}\n\n\n\n*Acceptance Criteria:*\n\n* Add a new field to the event body {{adjustedAmount}} representing de95 value (null for non-reversals, 0 for full reversals (if we send it))\n* Only {{DualMessageResponseV2}} is in scope\n* Updated component/unit tests for partial reversals."}, {"key": "MARS-2800", "title": "Implement partial reversals", "description": "*Business Value:* If we plan to support partial reversals for Diners this needs to be completed\n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *YES / NO*\n** (if yes - notify AG/RAG/Clearing)\n* Does it affect resources shared with Clearing? *YES* / *NO*\n** (if yes - notify Clearing)\n\n*Acceptance Criteria:*\n\n* Agree on approach for handling partial reversals, this seems to behave differently in Diners compared to other schemes as we’re expected to send the amount to be reversed, where for other schemes I believe we send the amount after the reversal?\n* [https://github.com/dojo-engineering/mars/blob/d414a050eb10a88f9c687e29b4ea827234da7bce/internal/auth/internal/scheme/diners/converters_reversal_requests.go#L36|https://github.com/dojo-engineering/mars/blob/d414a050eb10a88f9c687e29b4ea827234da7bce/internal/auth/internal/scheme/diners/converters_reversal_requests.go#L36]  the value sent here should be updated to send the correct values\n* [https://github.com/dojo-engineering/mars/blob/d414a050eb10a88f9c687e29b4ea827234da7bce/internal/auth/internal/scheme/diners/converters_reversal_requests.go#L45|https://github.com/dojo-engineering/mars/blob/d414a050eb10a88f9c687e29b4ea827234da7bce/internal/auth/internal/scheme/diners/converters_reversal_requests.go#L45]  we should validate this sends the correct value\n* Any partial reversal test cases should be updated to validate we’re sending the correct values in DE04 and DE30 "}], "scheme_examples": [{"key": "MARS-2810", "title": "enable cross-region access from AG txn svc to auth-amex", "description": "[Diagram of current proposed solution|https://lucid.app/lucidchart/2989ef11-5d82-4780-bd19-e9f943a1fc5a/edit?invitationId=inv_77dd7e8b-eec0-4778-ac08-9f35da367c81&page=iN~w_5tMaDZC#]\n\n\n\nThe proposed solution for removing local cluster URLs is as follows:\n\n* Set up static IP for ALBs \n** This has been completed already in staging [here|https://github.com/dojo-engineering/payments-infrastructure/blob/main/terraform/providers/google/dojo-payments-nonprod/nonprod/frankfurt/gke/terragrunt.hcl#L53] and [here|https://github.com/dojo-engineering/payments-infrastructure/blob/main/terraform/providers/google/dojo-payments-nonprod/nonprod/london/gke/terragrunt.hcl#L54], still needs finishing in prod\n* Generate internal ALBs with static IPs\n** this is done [here|https://github.com/dojo-engineering/payments-releases/blob/main/deployments/non-production/dojo-payments-nonprod/gke-nonprod-ld-1/cluster-services/istio-ingress-v2/istio-ingress-v2/values.yaml#L94] and [here|https://github.com/dojo-engineering/payments-releases/blob/main/deployments/non-production/dojo-payments-nonprod/gke-nonprod-ff-1/cluster-services/istio-ingress-v2/istio-ingress-v2/values.yaml#L43]\n** Currently this is not working as expected - the internal ALBs need to be created as global ALBs, but there’s no way to do this when creating them with k8s annotations, so this approach _may_ need to be rethought. [~accountid:5de502972fd6260cf27cdc0d] has requested guidance from David (04-12-24) - this ticket will be updated with any proposed changes. If David does not get back before Harry is on leave please whoever picks up this ticket follow up with David.\n* Update host alias in MARSGCP transaction services in AG + adjust endpoint URLs\n** This has been started for staging [here|https://github.com/dojo-engineering/auth-gateway/pull/4294] - until the ALB issue is resolved it cannot be properly tested so the PR remains in draft and unmerged.\n** *Note:* it was decided to retain the local cluster URLs for communication with services in the same cluster to keep things simple (e.g. AG MARSGCPLondon transaction service → auth-amex London should use a local cluster URL)\n*** that means we have to update MARSGCPLondon instance deployed in FF and MARSGCPFrankfurt instance deployed in London (2 configs) with IP addresses of above ALBs\n* Testing strategy\n** The current e2e testing strategy for this change is to use the [AGs end-to-end test tool|https://github.com/dojo-engineering/auth-gateway/tree/ce5410eb53950b19a70a44e5fbc8967ff3283d49/end-to-end-tests] to send requests to MARSGCPLondon in staging using the following command:\n*** {{./e2e payment staging-ld-purchase --processor=MARSGCPLondon --card-scheme=amex}}\n** [A GCP connectivity test|https://console.cloud.google.com/net-intelligence/connectivity/tests/list?invt=AbjCGg&project=dojo-payments-nonprod] ({{ldn-ff-peered-networking-test}}) has also been setup and may provide a faster feedback loop \n\n\n\n\n\nSoooo, I think I've found the issue, it basically boils down to the ILBs not being global, and there's no way for us to set that from k8s that I can tell. This is just something we're not going to be able to fix with the way we've currently got the ALBs setup\n\nThere's an [outstanding issue|https://issuetracker.google.com/issues/272577313] and something in the [k8s/ingress-gce|https://github.com/kubernetes/ingress-gce/issues/1887] repo suggesting that what we're trying to do is just not [http://possible.To|http://possible.To|smart-link]  speed up debugging I added this [ld-ff-peered-networking-test|https://console.cloud.google.com/net-intelligence/connectivity/tests/list?invt=AbjCGg&project=dojo-payments-nonprod] which is what pointed me in the direction in the first place\n\n* I've tried adding the {{networking.gke.io/internal-load-balancer-allow-global-access: \"true\"}} annotation to the service/ingress but it looks like this is just for l4 lbs - [https://cloud.google.com/kubernetes-engine/docs/concepts/service-load-balancer-parameters#global_access_internal_lb|https://cloud.google.com/kubernetes-engine/docs/concepts/service-load-balancer-parameters#global_access_internal_lb|smart-link] \n* I've tried adding a [GCPGatewayPolicy|https://cloud.google.com/kubernetes-engine/docs/how-to/configure-gateway-resources] but these only apply to {{gateway.networking.k8s.io}} - not our  Istio gateways\n* I tried manually creating a LB frontend with global access set to true, and that actually worked, but probably not something we'll want to do in prod (ss1)\n* I tried updating it with a gcloud command, that failed:\n\n{noformat}payments-infrastructure  (dojo-payments-nonprod)  test-global-lb-access ❯ gcloud beta compute forwarding-rules update k8s2-fs-8d36fjog-istio-ing-istio-ingress-v2-stg-auth-a-qy272bvf --region europe-west3 --allow-global-access                          \nERROR: (gcloud.beta.compute.forwarding-rules.update) Could not fetch resource:\n - Invalid value for field 'resource.allowGlobalAccess': 'true'. Updating the allow-global-access flag is only supported for regional INTERNAL forwarding rules with backend service/target instance.{noformat}\n\nin terms of potential fixes...\n\n* We could setup the global internal lb manually using [terraform|https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_forwarding_rule#example-usage---forwarding-rule-global-internallb]\n* We could make the lb external? Although that seems like it shouldn't be necessary when we have cluster networking peering setup?\n* Switch to an l4 LB and use the annotation above?\n\n\n\n*Business Value:*\n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *KINDA*\n** F2F AG will do the release since it changes their config - hence the notification will happen and AG will be in context\n* Does it affect resources shared with Clearing? *NO*\n\n*Acceptance Criteria:*"}, {"key": "MARS-2803", "title": "Re-enable zero-value auth incl. component tests for amex when certification is done", "description": "*Business Value:*\n\n*Acceptance Criteria:*\n\n[https://github.com/dojo-engineering/mars/blob/ff897776fc74ca362ad73725af56251d52ec2f9c/internal/auth/componenttest/authamex/certification_test.go#L80-L98|https://github.com/dojo-engineering/mars/blob/ff897776fc74ca362ad73725af56251d52ec2f9c/internal/auth/componenttest/authamex/certification_test.go#L80-L98] \n\n!Screenshot 2024-11-26 at 17.21.37.png|width=1129,height=470,alt=\"Screenshot 2024-11-26 at 17.21.37.png\"!"}], "file_patterns": ["internal/auth/internal/scheme/mc/converters_reversal_requests_test.go", "internal/auth/internal/scheme/mc/converters_reversal_requests.go", "componenttest/amexapi/request_v2.go", "go.mod", "go.sum", "internal/model/auth/generated.deepcopy.go", "internal/model/auth/reversal_request.go", "internal/model/mapping.go", "internal/model/testdata/v2_internal_reversal_request.json", "internal/model/testdata/v2_reversal_request.json"], "components": ["sqs_messaging"]}}