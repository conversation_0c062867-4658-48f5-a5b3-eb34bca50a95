=== PROMPT FOR IDEA 3 ===
Original Idea: Amex wants a reversal local datetime to be exactly like the time on a terminal
Timestamp: 2025-06-20T19:10:47.719767
Processing Time: 24.66 seconds
==================================================

You are a senior technical architect for a payment processing system that handles authorization and clearing for multiple card schemes (Visa, Mastercard, Amex, JCB, Diners, UnionPay).

DOMAIN EXPERTISE:
- Authorization: Real-time payment approval/decline decisions
- Clearing: Settlement and reconciliation of transactions
- Schemes: Each card scheme (Visa, MC, Amex, JCB) has specific rules and data formats
- ISO 8583: Standard message format for card transactions
- Data Elements (DE): Specific fields in transaction messages (DE3=processing code, DE7=datetime, etc.)
- Component Tests: Integration tests that validate end-to-end flows

ARCHITECTURE CONTEXT:
- System components: sqs_messaging

SIMILAR HISTORICAL TICKETS:
1. MARS-2745: use correct reversal reason values for MC
   Files changed: cmd/auth-fetcher/auth-fetcher, internal/auth/internal/scheme/mc/converters_reversal_requests_test.go, internal/auth/internal/scheme/mc/mappers.go
2. MARS-2400: AMEX tech debt
3. MARS-2507: Local date and time in DF12 should be in the same timezone for auth and reversals 
   Files changed: componenttest/amexapi/authorize_3ds_test.go, componenttest/amexapi/capture_test.go, componenttest/amexapi/certification_test.go

DOMAIN-SPECIFIC EXAMPLES:
- Set correct amount in recon events for partial reversals
- Implement partial reversals

COMMON FILE PATTERNS: internal/auth/internal/scheme/mc/converters_reversal_requests_test.go, internal/auth/internal/scheme/mc/converters_reversal_requests.go, componenttest/amexapi/request_v2.go, go.mod, go.sum

TASK: Refine this high-level idea into a comprehensive, actionable JIRA ticket:
IDEA: "Amex wants a reversal local datetime to be exactly like the time on a terminal"

REQUIREMENTS FOR OUTPUT:
1. Create specific, measurable acceptance criteria (NOT generic like 'feature implemented')
2. Include scheme-specific considerations if applicable
3. Provide concrete technical implementation details
4. Ask specific technical questions, not generic ones
5. Reference actual file paths and system components
6. Consider compliance and certification requirements

RESPONSE FORMAT:
TITLE: [Concise, actionable title]

BUSINESS_VALUE:
[Specific business impact and compliance benefits]

ACCEPTANCE_CRITERIA:
- [Specific, measurable criterion 1]
- [Specific, measurable criterion 2]
- [Continue with specific criteria...]

OPEN_QUESTIONS:
- [Specific technical question 1]
- [Specific business question 2]
- [Continue with specific questions...]

TECHNICAL_DESIGN:
[Detailed technical approach with specific components and file references]

LIKELY_FILES:
- [Specific file path 1]
- [Specific file path 2]
- [Continue with likely files...]

IMPACT_ASSESSMENT:
[Specific risks, dependencies, and notification requirements]

Generate a response that a payment processing engineer would find immediately actionable: