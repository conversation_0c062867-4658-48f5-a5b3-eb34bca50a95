#!/usr/bin/env python3
"""
Script to enrich JIRA tickets with file changes from git log.

This script takes jira_tickets.json and gitlog.log as input and produces
an enriched JSON list of JIRA tickets with associated file changes.
"""

import json
import re
import sys
from typing import Dict, List, Set, Tuple
from dataclasses import dataclass
from pathlib import Path


@dataclass
class FileChange:
    """Represents a file change in a git commit."""
    file_path: str
    change_type: str  # M=modified, A=added, D=deleted, R=renamed


@dataclass
class JiraTicket:
    """Represents a JIRA ticket with associated file changes."""
    key: str
    title: str
    description: str
    files_changed: List[FileChange]


def parse_git_log(git_log_path: str) -> Dict[str, List[FileChange]]:
    """
    Parse git log file and extract JIRA ticket to file changes mapping.
    
    Args:
        git_log_path: Path to the git log file
        
    Returns:
        Dictionary mapping JIRA ticket keys to list of file changes
    """
    ticket_to_files = {}
    
    with open(git_log_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Split into individual commits
    commits = re.split(r'^commit [a-f0-9]{40}$', content, flags=re.MULTILINE)
    
    for commit in commits:
        if not commit.strip():
            continue
            
        lines = commit.strip().split('\n')
        
        # Find JIRA ticket references in commit message
        jira_tickets = set()
        commit_message_lines = []
        file_changes = []
        
        # Parse commit structure
        in_message = False
        in_files = False
        
        for line in lines:
            line = line.strip()
            
            if line.startswith('Author:') or line.startswith('Date:'):
                continue
            elif not line and not in_message:
                in_message = True
                continue
            elif in_message and not in_files:
                if line and not line.startswith(('M\t', 'A\t', 'D\t', 'R')):
                    commit_message_lines.append(line)
                    # Look for JIRA ticket references
                    jira_matches = re.findall(r'(?:MARS-\d+)', line, re.IGNORECASE)
                    for match in jira_matches:
                        jira_tickets.add(match.upper())
                elif line.startswith(('M\t', 'A\t', 'D\t', 'R')):
                    in_files = True
                    # Process this file change line
                    file_changes.extend(parse_file_change_line(line))
            elif in_files:
                if line.startswith(('M\t', 'A\t', 'D\t', 'R')):
                    file_changes.extend(parse_file_change_line(line))
        
        # Associate file changes with JIRA tickets
        for ticket in jira_tickets:
            if ticket not in ticket_to_files:
                ticket_to_files[ticket] = []
            ticket_to_files[ticket].extend(file_changes)
    
    # Remove duplicates while preserving order
    for ticket in ticket_to_files:
        seen = set()
        unique_files = []
        for file_change in ticket_to_files[ticket]:
            file_key = (file_change.file_path, file_change.change_type)
            if file_key not in seen:
                seen.add(file_key)
                unique_files.append(file_change)
        ticket_to_files[ticket] = unique_files
    
    return ticket_to_files


def parse_file_change_line(line: str) -> List[FileChange]:
    """
    Parse a single file change line from git log.
    
    Args:
        line: Line like "M\tinternal/auth/app/authsimmc/handler.go"
        
    Returns:
        List of FileChange objects
    """
    changes = []
    
    # Handle different change types
    if line.startswith('M\t'):
        file_path = line[2:]
        changes.append(FileChange(file_path=file_path, change_type='M'))
    elif line.startswith('A\t'):
        file_path = line[2:]
        changes.append(FileChange(file_path=file_path, change_type='A'))
    elif line.startswith('D\t'):
        file_path = line[2:]
        changes.append(FileChange(file_path=file_path, change_type='D'))
    elif line.startswith('R'):
        # Handle renames like "R100\told_file\tnew_file"
        parts = line.split('\t')
        if len(parts) >= 3:
            old_file = parts[1]
            new_file = parts[2]
            changes.append(FileChange(file_path=old_file, change_type='D'))
            changes.append(FileChange(file_path=new_file, change_type='A'))
    
    return changes


def load_jira_tickets(jira_tickets_path: str) -> List[Dict]:
    """
    Load JIRA tickets from JSON file.
    
    Args:
        jira_tickets_path: Path to the JIRA tickets JSON file
        
    Returns:
        List of JIRA ticket dictionaries
    """
    with open(jira_tickets_path, 'r', encoding='utf-8') as f:
        return json.load(f)


def enrich_tickets(jira_tickets: List[Dict], ticket_to_files: Dict[str, List[FileChange]]) -> List[Dict]:
    """
    Enrich JIRA tickets with file changes.
    
    Args:
        jira_tickets: List of JIRA ticket dictionaries
        ticket_to_files: Mapping of ticket keys to file changes
        
    Returns:
        List of enriched JIRA ticket dictionaries
    """
    enriched_tickets = []
    
    for ticket in jira_tickets:
        enriched_ticket = ticket.copy()
        ticket_key = ticket['key']
        
        # Add file changes if any exist for this ticket
        if ticket_key in ticket_to_files:
            files_changed = []
            for file_change in ticket_to_files[ticket_key]:
                files_changed.append({
                    'file_path': file_change.file_path,
                    'change_type': file_change.change_type
                })
            enriched_ticket['files_changed'] = files_changed
        else:
            enriched_ticket['files_changed'] = []
        
        enriched_tickets.append(enriched_ticket)
    
    return enriched_tickets


def main():
    """Main function to orchestrate the enrichment process."""
    if len(sys.argv) != 3:
        print("Usage: python enrich_jira_tickets.py <jira_tickets.json> <gitlog.log>")
        sys.exit(1)
    
    jira_tickets_path = sys.argv[1]
    git_log_path = sys.argv[2]
    
    # Validate input files exist
    if not Path(jira_tickets_path).exists():
        print(f"Error: JIRA tickets file '{jira_tickets_path}' not found")
        sys.exit(1)
    
    if not Path(git_log_path).exists():
        print(f"Error: Git log file '{git_log_path}' not found")
        sys.exit(1)
    
    try:
        # Load JIRA tickets
        print(f"Loading JIRA tickets from {jira_tickets_path}...", file=sys.stderr)
        jira_tickets = load_jira_tickets(jira_tickets_path)
        print(f"Loaded {len(jira_tickets)} JIRA tickets", file=sys.stderr)

        # Parse git log
        print(f"Parsing git log from {git_log_path}...", file=sys.stderr)
        ticket_to_files = parse_git_log(git_log_path)
        print(f"Found file changes for {len(ticket_to_files)} JIRA tickets", file=sys.stderr)

        # Enrich tickets
        print("Enriching JIRA tickets with file changes...", file=sys.stderr)
        enriched_tickets = enrich_tickets(jira_tickets, ticket_to_files)

        # Output enriched JSON
        output = json.dumps(enriched_tickets, indent=2, ensure_ascii=False)
        print(output)

        # Print summary to stderr
        total_files = sum(len(ticket['files_changed']) for ticket in enriched_tickets)
        tickets_with_files = sum(1 for ticket in enriched_tickets if ticket['files_changed'])

        print(f"\nSummary:", file=sys.stderr)
        print(f"- Total tickets: {len(enriched_tickets)}", file=sys.stderr)
        print(f"- Tickets with file changes: {tickets_with_files}", file=sys.stderr)
        print(f"- Total file changes: {total_files}", file=sys.stderr)
        
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()
