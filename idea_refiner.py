#!/usr/bin/env python3
"""
LLM-Powered Idea Refiner for Payment Processing Systems

This script transforms high-level payment processing ideas into comprehensive,
domain-specific JIRA ticket descriptions using intelligent LLM prompting and
contextual knowledge injection.

Key Features:
- LLM-first approach with intelligent context management
- Payment processing domain expertise
- Smart similarity-based ticket retrieval
- Scheme-specific knowledge (Visa, MC, Amex, JCB)
- Architectural pattern recognition
- Actionable, specific acceptance criteria
"""

import argparse
import json
import logging
import re
import sys
import math
import time
from datetime import datetime
from dataclasses import dataclass, asdict
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple, Any
from collections import defaultdict, Counter
from functools import lru_cache

# Import MLX components for LLM integration
try:
    from mlx_lm import load, generate
    from mlx_lm.sample_utils import make_sampler
    MLX_AVAILABLE = True
except ImportError:
    MLX_AVAILABLE = False
    logging.warning("MLX not available - falling back to template mode")

# Model presets with context windows
PRESETS = {
    "gemma3": "/Users/<USER>/.lmstudio/models/mlx-community/gemma-3-27b-it-qat-4bit",
    "qwen3": "/Users/<USER>/.lmstudio/models/lmstudio-community/Qwen3-30B-A3B-MLX-4bit",
    "alma13": "/Users/<USER>/.lmstudio/models/mlx-community/ALMA-13B-R-4bit-mlx",
}

# Context windows for each model
MAX_CONTEXT = {
    "gemma3": 8192,
    "qwen3": 32768,
    "alma13": 4096,
}


class LLMDebugLogger:
    """Comprehensive logging for LLM interactions to enable efficient debugging."""

    def __init__(self, debug_dir: Optional[Path] = None, enabled: bool = True):
        self.enabled = enabled
        self.logger = logging.getLogger(__name__)

        if not self.enabled:
            self.debug_dir = None
            return

        # Create timestamped debug directory
        if debug_dir is None:
            debug_dir = Path("llm_debug_logs")

        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        self.debug_dir = debug_dir / timestamp
        self.debug_dir.mkdir(parents=True, exist_ok=True)

        self.logger.info(f"Debug logging enabled: {self.debug_dir}")

        # Initialize metadata
        self.metadata = {
            "start_time": datetime.now().isoformat(),
            "script_version": "LLM-powered idea refiner v2.0",
            "ideas_processed": 0,
            "errors": [],
            "processing_times": {}
        }

    def log_run_start(self, args: argparse.Namespace, model_preset: str):
        """Log script run parameters and configuration."""
        if not self.enabled:
            return

        self.metadata.update({
            "model_preset": model_preset,
            "ideas_file": str(args.ideas),
            "jira_tickets_file": str(args.jira_tickets),
            "enriched_tickets_file": str(args.enriched_tickets),
            "tree_log_file": str(args.tree_log),
            "git_log_file": str(args.git_log),
            "output_dir": str(args.output_dir),
            "log_level": args.log_level
        })

    def log_idea_processing(self, idea_num: int, idea: str, context: Dict[str, Any],
                           prompt: str, response: str, sections: Dict[str, str],
                           refined: 'RefinedIdea', processing_time: float):
        """Log complete LLM interaction for a single idea."""
        if not self.enabled:
            return

        idea_prefix = f"idea_{idea_num:02d}"

        # 1. Save prompt
        prompt_file = self.debug_dir / f"{idea_prefix}_prompt.txt"
        with open(prompt_file, 'w', encoding='utf-8') as f:
            f.write(f"=== PROMPT FOR IDEA {idea_num} ===\n")
            f.write(f"Original Idea: {idea}\n")
            f.write(f"Timestamp: {datetime.now().isoformat()}\n")
            f.write(f"Processing Time: {processing_time:.2f} seconds\n")
            f.write("=" * 50 + "\n\n")
            f.write(prompt)

        # 2. Save raw response
        response_file = self.debug_dir / f"{idea_prefix}_response.txt"
        with open(response_file, 'w', encoding='utf-8') as f:
            f.write(f"=== RAW LLM RESPONSE FOR IDEA {idea_num} ===\n")
            f.write(f"Original Idea: {idea}\n")
            f.write(f"Timestamp: {datetime.now().isoformat()}\n")
            f.write(f"Response Length: {len(response)} characters\n")
            f.write("=" * 50 + "\n\n")
            f.write(response)

        # 3. Save context (make it JSON serializable)
        context_file = self.debug_dir / f"{idea_prefix}_context.json"
        serializable_context = self._make_context_serializable(context)
        with open(context_file, 'w', encoding='utf-8') as f:
            json.dump({
                "idea_number": idea_num,
                "original_idea": idea,
                "timestamp": datetime.now().isoformat(),
                "context": serializable_context
            }, f, indent=2, ensure_ascii=False)

        # 4. Save parsed sections and final result
        parsed_file = self.debug_dir / f"{idea_prefix}_parsed.json"
        with open(parsed_file, 'w', encoding='utf-8') as f:
            json.dump({
                "idea_number": idea_num,
                "original_idea": idea,
                "timestamp": datetime.now().isoformat(),
                "extracted_sections": sections,
                "refined_idea": self._make_refined_idea_serializable(refined),
                "processing_time_seconds": processing_time
            }, f, indent=2, ensure_ascii=False)

        # Update metadata
        self.metadata["ideas_processed"] += 1
        self.metadata["processing_times"][f"idea_{idea_num}"] = processing_time

        self.logger.info(f"Debug logs saved for idea {idea_num}: {idea_prefix}_*.txt/json")

    def log_error(self, idea_num: int, idea: str, error: Exception):
        """Log processing errors."""
        if not self.enabled:
            return

        error_info = {
            "idea_number": idea_num,
            "idea": idea,
            "error_type": type(error).__name__,
            "error_message": str(error),
            "timestamp": datetime.now().isoformat()
        }

        self.metadata["errors"].append(error_info)

        # Save detailed error log
        error_file = self.debug_dir / f"idea_{idea_num:02d}_error.json"
        with open(error_file, 'w', encoding='utf-8') as f:
            json.dump(error_info, f, indent=2, ensure_ascii=False)

    def finalize_run(self):
        """Save final metadata and close logging."""
        if not self.enabled:
            return

        self.metadata["end_time"] = datetime.now().isoformat()
        self.metadata["total_runtime_seconds"] = (
            datetime.fromisoformat(self.metadata["end_time"]) -
            datetime.fromisoformat(self.metadata["start_time"])
        ).total_seconds()

        metadata_file = self.debug_dir / "run_metadata.json"
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(self.metadata, f, indent=2, ensure_ascii=False)

        self.logger.info(f"Debug session complete. Logs saved to: {self.debug_dir}")
        self.logger.info(f"Processed {self.metadata['ideas_processed']} ideas with {len(self.metadata['errors'])} errors")

    def _make_context_serializable(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Convert context to JSON-serializable format."""
        serializable = {}

        for key, value in context.items():
            if key == 'similar_tickets':
                # Convert ContextualTicket objects to dicts
                serializable[key] = [
                    {
                        'ticket': ctx_ticket.ticket,
                        'similarity_score': ctx_ticket.similarity_score,
                        'file_changes': ctx_ticket.file_changes
                    }
                    for ctx_ticket in value
                ]
            else:
                serializable[key] = value

        return serializable

    def _make_refined_idea_serializable(self, refined: 'RefinedIdea') -> Dict[str, Any]:
        """Convert RefinedIdea to JSON-serializable format."""
        return {
            "original_idea": refined.original_idea,
            "title": refined.title,
            "business_value": refined.business_value,
            "description": refined.description,
            "acceptance_criteria": refined.acceptance_criteria,
            "open_questions": refined.open_questions,
            "suggested_design": refined.suggested_design,
            "likely_files": refined.likely_files,
            "impact_assessment": refined.impact_assessment,
            "related_tickets": refined.related_tickets,
            "context_used_keys": list(refined.context_used.keys()) if refined.context_used else []
        }


@dataclass
class ProjectKnowledge:
    """Container for all project knowledge extracted from input files."""
    tickets: List[Dict]
    enriched_tickets: List[Dict]
    project_structure: List[str]
    git_commits: List[str]

    # Derived knowledge for LLM context
    scheme_patterns: Dict[str, List[Dict]]  # Visa, MC, Amex, JCB specific tickets
    file_change_patterns: Dict[str, List[str]]  # Common file patterns by domain
    architectural_components: Set[str]  # Key system components
    domain_examples: Dict[str, List[Dict]]  # Example tickets by domain


@dataclass
class ContextualTicket:
    """A ticket with similarity score for context ranking."""
    ticket: Dict
    similarity_score: float
    file_changes: List[str]


@dataclass
class RefinedIdea:
    """Container for a refined idea with LLM-generated content."""
    original_idea: str
    title: str
    business_value: str
    description: str
    acceptance_criteria: List[str]
    open_questions: List[str]
    suggested_design: str
    likely_files: List[str]
    impact_assessment: str
    related_tickets: List[str]
    context_used: Dict[str, Any]  # Track what context was provided to LLM


class SmartKnowledgeExtractor:
    """Extracts and organizes knowledge for intelligent LLM context injection."""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def extract_knowledge(self, jira_path: Path, enriched_path: Path,
                         tree_path: Path, git_path: Path) -> ProjectKnowledge:
        """Extract and organize knowledge for LLM context."""
        self.logger.info("Extracting payment processing domain knowledge...")

        # Load raw data
        tickets = self._load_json(jira_path)
        enriched_tickets = self._load_json(enriched_path)
        project_structure = self._load_text_lines(tree_path)
        git_commits = self._load_text_lines(git_path)

        # Organize by payment schemes and domains
        scheme_patterns = self._extract_scheme_patterns(tickets)
        file_change_patterns = self._extract_file_patterns(enriched_tickets)
        architectural_components = self._extract_architectural_components(project_structure)
        domain_examples = self._extract_domain_examples(tickets, enriched_tickets)

        self.logger.info(f"Organized {len(tickets)} tickets into domain-specific patterns")
        self.logger.info(f"Found {len(scheme_patterns)} scheme-specific patterns")

        return ProjectKnowledge(
            tickets=tickets,
            enriched_tickets=enriched_tickets,
            project_structure=project_structure,
            git_commits=git_commits,
            scheme_patterns=scheme_patterns,
            file_change_patterns=file_change_patterns,
            architectural_components=architectural_components,
            domain_examples=domain_examples
        )
    
    def _load_json(self, path: Path) -> List[Dict]:
        """Load JSON file safely."""
        try:
            with open(path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"Failed to load {path}: {e}")
            return []

    def _load_text_lines(self, path: Path) -> List[str]:
        """Load text file as lines."""
        try:
            with open(path, 'r', encoding='utf-8') as f:
                return [line.strip() for line in f if line.strip()]
        except Exception as e:
            self.logger.error(f"Failed to load {path}: {e}")
            return []

    def _extract_scheme_patterns(self, tickets: List[Dict]) -> Dict[str, List[Dict]]:
        """Extract tickets by payment scheme for scheme-specific context."""
        schemes = {
            'visa': ['visa'],
            'mastercard': ['mc', 'mastercard'],
            'amex': ['amex', 'american express'],
            'jcb': ['jcb'],
            'diners': ['diners'],
            'unionpay': ['unionpay']
        }

        scheme_tickets = defaultdict(list)

        for ticket in tickets:
            content = f"{ticket.get('title', '')} {ticket.get('description', '')}".lower()
            for scheme, keywords in schemes.items():
                if any(keyword in content for keyword in keywords):
                    scheme_tickets[scheme].append(ticket)

        return dict(scheme_tickets)

    def _extract_domain_examples(self, tickets: List[Dict], enriched_tickets: List[Dict]) -> Dict[str, List[Dict]]:
        """Extract example tickets by domain for context."""
        domains = {
            'authorization': ['auth', 'authorization', 'approve', 'decline'],
            'clearing': ['clearing', 'settlement', 'reconcile'],
            'reversal': ['reversal', 'void', 'cancel'],
            'migration': ['migration', 'migrate', 'database', 'schema'],
            'messaging': ['kafka', 'sqs', 'queue', 'message'],
            'validation': ['validation', 'validate', 'block', 'reject'],
            'datetime': ['datetime', 'timestamp', 'time', 'timezone']
        }

        domain_tickets = defaultdict(list)

        # Create enriched lookup
        enriched_lookup = {t['key']: t for t in enriched_tickets}

        for ticket in tickets:
            content = f"{ticket.get('title', '')} {ticket.get('description', '')}".lower()
            for domain, keywords in domains.items():
                if any(keyword in content for keyword in keywords):
                    # Add file changes if available
                    enriched = enriched_lookup.get(ticket.get('key'))
                    if enriched and enriched.get('files_changed'):
                        ticket_with_files = ticket.copy()
                        ticket_with_files['files_changed'] = enriched['files_changed']
                        domain_tickets[domain].append(ticket_with_files)
                    else:
                        domain_tickets[domain].append(ticket)

        return dict(domain_tickets)

    def _extract_file_patterns(self, enriched_tickets: List[Dict]) -> Dict[str, List[str]]:
        """Extract file change patterns by domain."""
        patterns = defaultdict(list)

        for ticket in enriched_tickets:
            title = ticket.get('title', '').lower()
            files_changed = ticket.get('files_changed', [])

            if not files_changed:
                continue

            file_paths = [f['file_path'] for f in files_changed]

            # Categorize by content
            if any(word in title for word in ['auth', 'authorization']):
                patterns['auth'].extend(file_paths)
            elif any(word in title for word in ['clearing', 'settlement']):
                patterns['clearing'].extend(file_paths)
            elif any(word in title for word in ['reversal', 'void']):
                patterns['reversal'].extend(file_paths)
            elif any(word in title for word in ['migration', 'database']):
                patterns['migration'].extend(file_paths)
            elif any(word in title for word in ['kafka', 'sqs', 'message']):
                patterns['messaging'].extend(file_paths)

        # Remove duplicates and get most common
        for domain in patterns:
            file_counter = Counter(patterns[domain])
            patterns[domain] = [file for file, count in file_counter.most_common(10)]

        return dict(patterns)

    def _extract_architectural_components(self, project_structure: List[str]) -> Set[str]:
        """Extract key architectural components from project structure."""
        components = set()

        for line in project_structure:
            if 'internal/auth/' in line:
                components.add('auth_services')
            if 'internal/clearing/' in line:
                components.add('clearing_services')
            if 'internal/scheme/' in line:
                components.add('scheme_handlers')
            if 'componenttest/' in line:
                components.add('component_tests')
            if 'helm/' in line:
                components.add('kubernetes_deployment')
            if 'migrations/' in line:
                components.add('database_migrations')
            if 'kafka' in line.lower():
                components.add('kafka_messaging')
            if 'sqs' in line.lower():
                components.add('sqs_messaging')

        return components


class IntelligentLLMRefiner:
    """LLM-powered refiner with intelligent context injection and domain expertise."""

    def __init__(self, model_preset: str = "qwen3", debug_logger: Optional[LLMDebugLogger] = None):
        self.logger = logging.getLogger(__name__)
        self.model_preset = model_preset
        self.max_context = MAX_CONTEXT.get(model_preset, 4096)
        self.debug_logger = debug_logger

        if not MLX_AVAILABLE:
            self.logger.error("MLX not available - LLM refinement requires MLX")
            raise RuntimeError("MLX required for LLM-based refinement")

        try:
            model_path = PRESETS[model_preset]
            self.logger.info(f"Loading {model_preset} model from {model_path}")
            self.model, self.tokenizer = load(model_path)
            self.logger.info(f"Model loaded successfully (context: {self.max_context} tokens)")
        except Exception as e:
            self.logger.error(f"Failed to load model: {e}")
            raise

    @lru_cache(maxsize=1000)
    def _count_tokens(self, text: str) -> int:
        """Count tokens in text using model tokenizer."""
        try:
            return len(self.tokenizer.encode(text))
        except Exception:
            return len(text) // 4  # Rough estimate

    def refine_idea(self, idea: str, knowledge: ProjectKnowledge, idea_num: int = 0) -> RefinedIdea:
        """Refine idea using LLM with JSON output, validation, and retry logic."""
        start_time = time.time()
        self.logger.info(f"Refining idea {idea_num} with LLM (JSON mode): {idea[:60]}...")

        max_retries = 2
        last_error = None

        for attempt in range(max_retries + 1):
            try:
                # Build contextual prompt with relevant knowledge
                context = self._build_intelligent_context(idea, knowledge)
                prompt = self._create_domain_aware_prompt(idea, context)

                # Generate response
                response = self._generate_llm_response(prompt)

                # Parse JSON response with validation
                refined = self._parse_llm_response(idea, response, context)

                processing_time = time.time() - start_time

                # For debug logging, we need sections for compatibility
                sections = {"json_response": response}

                # Log debug information
                if self.debug_logger:
                    self.debug_logger.log_idea_processing(
                        idea_num, idea, context, prompt, response, sections, refined, processing_time
                    )

                self.logger.info(f"Generated refined ticket {idea_num}: {refined.title} ({processing_time:.2f}s)")
                if attempt > 0:
                    self.logger.info(f"Success on retry attempt {attempt}")

                return refined

            except json.JSONDecodeError as e:
                last_error = e
                self.logger.warning(f"JSON parsing failed on attempt {attempt + 1}: {e}")
                if attempt < max_retries:
                    self.logger.info(f"Retrying with more explicit JSON prompt...")
                    # Could modify prompt here for retry
                    continue
                else:
                    self.logger.error(f"All JSON parsing attempts failed for idea {idea_num}")

            except Exception as e:
                last_error = e
                self.logger.error(f"Failed to refine idea {idea_num} on attempt {attempt + 1}: {e}")
                if attempt < max_retries:
                    continue
                else:
                    break

        # If we get here, all attempts failed
        processing_time = time.time() - start_time

        # Log error
        if self.debug_logger:
            self.debug_logger.log_error(idea_num, idea, last_error)

        raise RuntimeError(f"Failed to refine idea {idea_num} after {max_retries + 1} attempts: {last_error}")

    def _build_intelligent_context(self, idea: str, knowledge: ProjectKnowledge) -> Dict[str, Any]:
        """Build intelligent context based on idea similarity and relevance."""
        context = {}

        # Find most relevant tickets by similarity
        relevant_tickets = self._find_similar_tickets(idea, knowledge.tickets, knowledge.enriched_tickets)
        context['similar_tickets'] = relevant_tickets[:3]  # Top 3 most similar

        # Get domain-specific examples
        domain = self._identify_primary_domain(idea)
        if domain in knowledge.domain_examples:
            context['domain_examples'] = knowledge.domain_examples[domain][:2]

        # Get scheme-specific context if applicable
        scheme = self._identify_scheme(idea)
        if scheme and scheme in knowledge.scheme_patterns:
            context['scheme_examples'] = knowledge.scheme_patterns[scheme][:2]

        # Get relevant file patterns
        if domain in knowledge.file_change_patterns:
            context['file_patterns'] = knowledge.file_change_patterns[domain]

        # Add architectural context
        context['components'] = list(knowledge.architectural_components)

        # Estimate context size and trim if needed
        context = self._optimize_context_size(context)

        return context

    def _find_similar_tickets(self, idea: str, tickets: List[Dict], enriched_tickets: List[Dict]) -> List[ContextualTicket]:
        """Find tickets most similar to the idea using keyword overlap."""
        idea_words = set(idea.lower().split())
        enriched_lookup = {t['key']: t for t in enriched_tickets}

        scored_tickets = []

        for ticket in tickets:
            title = ticket.get('title', '').lower()
            desc = ticket.get('description', '').lower()
            content_words = set(f"{title} {desc}".split())

            # Calculate similarity score
            overlap = len(idea_words.intersection(content_words))
            total_words = len(idea_words.union(content_words))
            similarity = overlap / total_words if total_words > 0 else 0

            if similarity > 0.1:  # Minimum threshold
                enriched = enriched_lookup.get(ticket.get('key'))
                file_changes = []
                if enriched and enriched.get('files_changed'):
                    file_changes = [f['file_path'] for f in enriched['files_changed']]

                scored_tickets.append(ContextualTicket(
                    ticket=ticket,
                    similarity_score=similarity,
                    file_changes=file_changes
                ))

        # Sort by similarity score
        scored_tickets.sort(key=lambda x: x.similarity_score, reverse=True)
        return scored_tickets

    def _identify_primary_domain(self, idea: str) -> str:
        """Identify the primary domain of the idea."""
        idea_lower = idea.lower()

        domain_keywords = {
            'authorization': ['auth', 'authorization', 'approve', 'decline', 'block'],
            'clearing': ['clearing', 'settlement', 'reconcile'],
            'reversal': ['reversal', 'void', 'cancel'],
            'migration': ['migration', 'migrate', 'database', 'schema', 'squash'],
            'messaging': ['kafka', 'sqs', 'queue', 'message'],
            'validation': ['validation', 'validate', 'block', 'reject'],
            'datetime': ['datetime', 'timestamp', 'time', 'timezone', 'local']
        }

        for domain, keywords in domain_keywords.items():
            if any(keyword in idea_lower for keyword in keywords):
                return domain

        return 'general'

    def _identify_scheme(self, idea: str) -> Optional[str]:
        """Identify if idea is specific to a payment scheme."""
        idea_lower = idea.lower()

        if any(word in idea_lower for word in ['mc', 'mastercard']):
            return 'mastercard'
        elif 'visa' in idea_lower:
            return 'visa'
        elif 'amex' in idea_lower:
            return 'amex'
        elif 'jcb' in idea_lower:
            return 'jcb'
        elif 'diners' in idea_lower:
            return 'diners'

        return None

    def _optimize_context_size(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize context size to fit within model limits."""
        # Reserve tokens for prompt template and response
        available_tokens = self.max_context - 2000  # Reserve for prompt + response

        # Convert context to serializable format for size estimation
        serializable_context = self._make_context_serializable(context)
        context_text = json.dumps(serializable_context, indent=2)
        current_tokens = self._count_tokens(context_text)

        if current_tokens <= available_tokens:
            return context

        # Trim context intelligently
        optimized = context.copy()

        # Reduce similar tickets if needed
        if 'similar_tickets' in optimized and len(optimized['similar_tickets']) > 1:
            optimized['similar_tickets'] = optimized['similar_tickets'][:1]

        # Reduce domain examples
        if 'domain_examples' in optimized and len(optimized['domain_examples']) > 1:
            optimized['domain_examples'] = optimized['domain_examples'][:1]

        # Limit file patterns
        if 'file_patterns' in optimized and len(optimized['file_patterns']) > 5:
            optimized['file_patterns'] = optimized['file_patterns'][:5]

        return optimized

    def _make_context_serializable(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Convert context to JSON-serializable format."""
        serializable = {}

        for key, value in context.items():
            if key == 'similar_tickets':
                # Convert ContextualTicket objects to dicts
                serializable[key] = [
                    {
                        'ticket': ctx_ticket.ticket,
                        'similarity_score': ctx_ticket.similarity_score,
                        'file_changes': ctx_ticket.file_changes
                    }
                    for ctx_ticket in value
                ]
            else:
                serializable[key] = value

        return serializable

    def _create_domain_aware_prompt(self, idea: str, context: Dict[str, Any]) -> str:
        """Create JSON-focused prompt for structured output."""

        # Build context sections
        context_sections = []

        # Add similar tickets for context
        if context.get('similar_tickets'):
            context_sections.append("SIMILAR HISTORICAL TICKETS:")
            for i, ctx_ticket in enumerate(context['similar_tickets'], 1):
                ticket = ctx_ticket.ticket
                context_sections.append(f"{i}. {ticket.get('key', 'UNKNOWN')}: {ticket.get('title', 'No title')}")
                if ctx_ticket.file_changes:
                    context_sections.append(f"   Files changed: {', '.join(ctx_ticket.file_changes[:3])}")

        # Add domain examples
        if context.get('domain_examples'):
            context_sections.append("\nDOMAIN-SPECIFIC EXAMPLES:")
            for example in context['domain_examples']:
                context_sections.append(f"- {example.get('title', 'No title')}")

        # Add file patterns
        if context.get('file_patterns'):
            context_sections.append(f"\nCOMMON FILE PATTERNS: {', '.join(context['file_patterns'][:5])}")

        context_text = "\n".join(context_sections) if context_sections else "No specific context available."

        prompt = f"""/no_think

You are a senior technical architect for a payment processing system handling authorization and clearing for multiple card schemes (Visa, Mastercard, Amex, JCB, Diners, UnionPay).

DOMAIN KNOWLEDGE:
- Authorization: Real-time payment approval/decline decisions
- Clearing: Settlement and reconciliation of transactions
- Schemes: Each card scheme has specific rules and data formats
- ISO 8583: Standard message format for card transactions
- Data Elements (DE): Specific fields in transaction messages (DE3=processing code, DE7=datetime, etc.)
- Component Tests: Integration tests that validate end-to-end flows

SYSTEM COMPONENTS: {', '.join(context.get('components', []))}

PROJECT CONTEXT:
{context_text}

TASK: Refine this payment processing idea into a comprehensive JIRA ticket:
IDEA: "{idea}"

REQUIREMENTS:
1. Create specific, measurable acceptance criteria (NOT generic like 'feature implemented')
2. Include scheme-specific considerations if applicable
3. Provide concrete technical implementation details
4. Ask specific technical questions, not generic ones
5. Reference actual file paths and system components
6. Consider compliance and certification requirements

CRITICAL: Respond ONLY with valid JSON. Do not include any explanatory text, reasoning, or markdown formatting outside the JSON structure.

JSON SCHEMA REQUIRED:
{{
  "title": "Concise, actionable title",
  "business_value": "Specific business impact and compliance benefits",
  "acceptance_criteria": [
    "Specific, measurable criterion with technical details",
    "Another specific criterion with scheme references",
    "Criterion with file/component references"
  ],
  "open_questions": [
    "Specific technical question about implementation",
    "Specific business question about requirements",
    "Question about compliance or certification"
  ],
  "technical_design": "Detailed technical approach with specific components, file references, and implementation steps",
  "likely_files": [
    "path/to/specific/file1.java",
    "path/to/specific/file2.yaml",
    "path/to/test/file.java"
  ],
  "impact_assessment": "Specific risks, dependencies, and notification requirements"
}}

Generate JSON response for a payment processing engineer:"""

        return prompt

    def _generate_llm_response(self, prompt: str) -> str:
        """Generate response using LLM."""
        try:
            # Check prompt size
            prompt_tokens = self._count_tokens(prompt)
            max_response_tokens = min(2048, self.max_context - prompt_tokens - 100)

            if max_response_tokens < 500:
                self.logger.warning(f"Limited response tokens: {max_response_tokens}")

            self.logger.debug(f"Prompt tokens: {prompt_tokens}, Max response: {max_response_tokens}")

            sampler = make_sampler(temp=0.3, top_p=0.9)
            response = generate(
                self.model,
                self.tokenizer,
                prompt=prompt,
                max_tokens=max_response_tokens,
                sampler=sampler,
                verbose=False
            )

            # Log response for debugging
            self.logger.debug(f"LLM Response: {response[:200]}...")

            return response.strip()

        except Exception as e:
            self.logger.error(f"LLM generation failed: {e}")
            raise

    def _parse_llm_response(self, idea: str, response: str, context: Dict[str, Any]) -> RefinedIdea:
        """Parse JSON LLM response into structured RefinedIdea with validation and fallback."""

        # Try to parse JSON response
        try:
            parsed_json = self._extract_and_validate_json(response)

            # Extract data from JSON
            title = parsed_json.get('title', idea.strip())
            business_value = parsed_json.get('business_value', 'Business value to be determined')
            acceptance_criteria = parsed_json.get('acceptance_criteria', [])
            open_questions = parsed_json.get('open_questions', [])
            technical_design = parsed_json.get('technical_design', 'Technical design to be determined')
            likely_files = parsed_json.get('likely_files', [])
            impact_assessment = parsed_json.get('impact_assessment', 'Impact assessment to be determined')

            # Validate lists are actually lists
            if not isinstance(acceptance_criteria, list):
                acceptance_criteria = [str(acceptance_criteria)] if acceptance_criteria else []
            if not isinstance(open_questions, list):
                open_questions = [str(open_questions)] if open_questions else []
            if not isinstance(likely_files, list):
                likely_files = [str(likely_files)] if likely_files else []

        except Exception as e:
            self.logger.warning(f"JSON parsing failed: {e}. Attempting fallback parsing.")
            # Fallback to text parsing if JSON fails
            sections = self._extract_sections(response)
            title = sections.get('TITLE', idea.strip())
            business_value = sections.get('BUSINESS_VALUE', 'Business value to be determined')
            acceptance_criteria = self._parse_list_section(sections.get('ACCEPTANCE_CRITERIA', ''))
            open_questions = self._parse_list_section(sections.get('OPEN_QUESTIONS', ''))
            likely_files = self._parse_list_section(sections.get('LIKELY_FILES', ''))
            technical_design = sections.get('TECHNICAL_DESIGN', 'Technical design to be determined')
            impact_assessment = sections.get('IMPACT_ASSESSMENT', 'Impact assessment to be determined')

        # Build description
        description = self._build_jira_description(
            business_value, acceptance_criteria, open_questions,
            technical_design, impact_assessment
        )

        # Extract related tickets from context
        related_tickets = []
        if context.get('similar_tickets'):
            for ctx_ticket in context['similar_tickets']:
                ticket = ctx_ticket.ticket
                related_tickets.append(f"{ticket.get('key', 'UNKNOWN')}: {ticket.get('title', 'No title')}")

        return RefinedIdea(
            original_idea=idea,
            title=title,
            business_value=business_value,
            description=description,
            acceptance_criteria=acceptance_criteria,
            open_questions=open_questions,
            suggested_design=technical_design,
            likely_files=likely_files,
            impact_assessment=impact_assessment,
            related_tickets=related_tickets,
            context_used=context
        )

    def _extract_and_validate_json(self, response: str) -> Dict[str, Any]:
        """Extract and validate JSON from LLM response."""
        # Clean the response
        cleaned = response.strip()

        # Remove any text before the first {
        start_idx = cleaned.find('{')
        if start_idx == -1:
            raise ValueError("No JSON object found in response")

        # Remove any text after the last }
        end_idx = cleaned.rfind('}')
        if end_idx == -1:
            raise ValueError("No complete JSON object found in response")

        json_str = cleaned[start_idx:end_idx + 1]

        # Parse JSON
        try:
            parsed = json.loads(json_str)
        except json.JSONDecodeError as e:
            # Try to fix common JSON issues
            json_str = self._fix_common_json_issues(json_str)
            parsed = json.loads(json_str)

        # Validate required fields
        required_fields = ['title', 'business_value', 'acceptance_criteria',
                          'open_questions', 'technical_design', 'likely_files',
                          'impact_assessment']

        for field in required_fields:
            if field not in parsed:
                self.logger.warning(f"Missing required field: {field}")
                parsed[field] = f"{field.replace('_', ' ').title()} to be determined"

        return parsed

    def _fix_common_json_issues(self, json_str: str) -> str:
        """Fix common JSON formatting issues from LLM responses."""
        # Fix trailing commas
        json_str = re.sub(r',(\s*[}\]])', r'\1', json_str)

        # Fix unescaped quotes in strings
        json_str = re.sub(r'(?<!\\)"(?=.*")', r'\\"', json_str)

        # Fix single quotes to double quotes
        json_str = re.sub(r"'([^']*)':", r'"\1":', json_str)

        return json_str

    def _extract_sections(self, response: str) -> Dict[str, str]:
        """Extract sections from LLM response, handling various formats and cleaning up."""
        sections = {}

        # Clean response - remove model artifacts and reasoning
        cleaned_response = self._clean_llm_response(response)

        # Find the structured part of the response (after any preamble)
        lines = cleaned_response.split('\n')
        start_idx = 0

        # Look for the first section header
        section_headers = ['TITLE:', 'BUSINESS_VALUE:', 'ACCEPTANCE_CRITERIA:',
                          'OPEN_QUESTIONS:', 'TECHNICAL_DESIGN:', 'LIKELY_FILES:',
                          'IMPACT_ASSESSMENT:']

        for i, line in enumerate(lines):
            if any(line.strip().startswith(header) for header in section_headers):
                start_idx = i
                break

        # Parse from the structured part
        current_section = None
        current_content = []

        for line in lines[start_idx:]:
            line = line.strip()

            # Skip empty lines at start of sections
            if not line and not current_content:
                continue

            # Stop if we hit model reasoning or artifacts
            if any(stop_phrase in line.lower() for stop_phrase in [
                'okay, i need to', 'let me start', 'first, the title',
                'business value:', 'for acceptance criteria', 'i think that',
                'make sure', 'i need to make sure', 'check if'
            ]):
                break

            header_found = None
            for header in section_headers:
                if line.startswith(header):
                    header_found = header.rstrip(':')
                    break

            if header_found:
                # Save previous section
                if current_section and current_content:
                    sections[current_section] = '\n'.join(current_content).strip()

                # Start new section
                current_section = header_found
                current_content = []

                # Check if content is on same line
                content_after_header = line[len(header_found) + 1:].strip()
                if content_after_header:
                    current_content.append(content_after_header)
            else:
                if current_section and line:
                    current_content.append(line)

        # Save final section
        if current_section and current_content:
            sections[current_section] = '\n'.join(current_content).strip()

        return sections

    def _clean_llm_response(self, response: str) -> str:
        """Clean LLM response by removing artifacts and reasoning."""
        # Remove common LLM artifacts
        cleaned = response

        # Remove end-of-text tokens
        cleaned = re.sub(r'<\|endoftext\|>.*$', '', cleaned, flags=re.DOTALL)

        # Remove "Human:" and subsequent content
        cleaned = re.sub(r'Human:.*$', '', cleaned, flags=re.DOTALL)

        # Remove model reasoning patterns
        reasoning_patterns = [
            r'Okay, I need to.*?(?=TITLE:|$)',
            r'Let me start.*?(?=TITLE:|$)',
            r'First, the title.*?(?=TITLE:|$)',
            r'I need to make sure.*?(?=TITLE:|$)',
            r'no fluff, no generic statements.*?(?=TITLE:|$)'
        ]

        for pattern in reasoning_patterns:
            cleaned = re.sub(pattern, '', cleaned, flags=re.DOTALL | re.IGNORECASE)

        return cleaned.strip()

    def _parse_list_section(self, content: str) -> List[str]:
        """Parse list section into individual items."""
        if not content:
            return []

        items = []
        for line in content.split('\n'):
            line = line.strip()
            if line.startswith('- '):
                items.append(line[2:].strip())
            elif line.startswith('* '):
                items.append(line[2:].strip())
            elif line and not line.startswith(' '):
                items.append(line)

        return [item for item in items if item]

    def _build_jira_description(self, business_value: str, acceptance_criteria: List[str],
                               open_questions: List[str], technical_design: str,
                               impact_assessment: str) -> str:
        """Build JIRA-formatted description."""
        parts = [
            f"*Business Value:*\n{business_value}\n",
            "*Acceptance Criteria:*"
        ]

        for criteria in acceptance_criteria:
            parts.append(f"* {criteria}")

        parts.extend(["\n*Open Questions:*"])
        for question in open_questions:
            parts.append(f"* {question}")

        parts.extend([
            f"\n*Technical Design:*\n{technical_design}\n",
            f"*Impact and Notifications:*\n{impact_assessment}"
        ])

        return "\n".join(parts)
    
    def _extract_ticket_patterns(self, tickets: List[Dict]) -> Dict[str, List[str]]:
        """Extract common patterns from ticket descriptions."""
        patterns = defaultdict(list)
        
        for ticket in tickets:
            desc = ticket.get('description', '')
            title = ticket.get('title', '')
            
            # Extract business value patterns
            if '*Business Value:*' in desc:
                bv_match = re.search(r'\*Business Value:\*\s*([^*]+)', desc)
                if bv_match:
                    patterns['business_value'].append(bv_match.group(1).strip())
            
            # Extract acceptance criteria patterns
            if '*Acceptance Criteria:*' in desc:
                ac_match = re.search(r'\*Acceptance Criteria:\*\s*([^*]+)', desc)
                if ac_match:
                    patterns['acceptance_criteria'].append(ac_match.group(1).strip())
            
            # Extract impact patterns
            if '*Impact and notifications:*' in desc:
                impact_match = re.search(r'\*Impact and notifications:\*\s*([^*]+)', desc)
                if impact_match:
                    patterns['impact'].append(impact_match.group(1).strip())
        
        return dict(patterns)
    
    def _extract_file_patterns(self, enriched_tickets: List[Dict]) -> Dict[str, List[str]]:
        """Extract file change patterns by ticket type."""
        patterns = defaultdict(list)
        
        for ticket in enriched_tickets:
            title = ticket.get('title', '').lower()
            files_changed = ticket.get('files_changed', [])
            
            if not files_changed:
                continue
            
            # Categorize by ticket type
            if any(word in title for word in ['auth', 'authorization']):
                patterns['auth'].extend([f['file_path'] for f in files_changed])
            elif any(word in title for word in ['clearing', 'settlement']):
                patterns['clearing'].extend([f['file_path'] for f in files_changed])
            elif any(word in title for word in ['api', 'endpoint']):
                patterns['api'].extend([f['file_path'] for f in files_changed])
            elif any(word in title for word in ['test', 'testing']):
                patterns['testing'].extend([f['file_path'] for f in files_changed])
            elif any(word in title for word in ['config', 'configuration']):
                patterns['config'].extend([f['file_path'] for f in files_changed])
        
        return dict(patterns)
    
    def _extract_business_domains(self, tickets: List[Dict]) -> Set[str]:
        """Extract business domains from ticket content."""
        domains = set()
        
        for ticket in tickets:
            title = ticket.get('title', '').lower()
            desc = ticket.get('description', '').lower()
            content = f"{title} {desc}"
            
            # Common business domains in payment processing
            if any(word in content for word in ['visa', 'mastercard', 'mc']):
                domains.add('card_schemes')
            if any(word in content for word in ['amex', 'american express']):
                domains.add('amex')
            if any(word in content for word in ['auth', 'authorization']):
                domains.add('authorization')
            if any(word in content for word in ['clearing', 'settlement']):
                domains.add('clearing')
            if any(word in content for word in ['fraud', 'risk']):
                domains.add('fraud_prevention')
            if any(word in content for word in ['terminal', 'pos']):
                domains.add('terminal_management')
            if any(word in content for word in ['merchant', 'customer']):
                domains.add('merchant_services')
        
        return domains
    
    def _extract_technical_components(self, project_structure: List[str]) -> Set[str]:
        """Extract technical components from project structure."""
        components = set()
        
        for line in project_structure:
            if 'auth-' in line:
                components.add('auth_services')
            if 'clearing-' in line:
                components.add('clearing_services')
            if 'helm' in line:
                components.add('kubernetes')
            if 'config' in line:
                components.add('configuration')
            if 'test' in line:
                components.add('testing')
            if 'internal/' in line:
                components.add('internal_apis')
            if 'cmd/' in line:
                components.add('applications')
        
        return components


class LLMRefiner:
    """Uses LLM to refine ideas into comprehensive ticket descriptions."""
    
    def __init__(self, model_preset: str = "qwen3"):
        self.logger = logging.getLogger(__name__)
        self.model_preset = model_preset
        
        if not MLX_AVAILABLE:
            self.logger.warning("MLX not available - using template-based refinement")
            self.model = None
            self.tokenizer = None
            return
        
        try:
            model_path = PRESETS[model_preset]
            self.logger.info(f"Loading model from {model_path}")
            self.model, self.tokenizer = load(model_path)
            self.logger.info("Model loaded successfully")
        except Exception as e:
            self.logger.error(f"Failed to load model: {e}")
            self.model = None
            self.tokenizer = None
    
    def refine_idea(self, idea: str, knowledge: ProjectKnowledge) -> RefinedIdea:
        """Refine a single idea into a comprehensive ticket description."""
        self.logger.info(f"Refining idea: {idea}")
        
        if self.model is None:
            return self._template_based_refinement(idea, knowledge)
        else:
            return self._llm_based_refinement(idea, knowledge)
    
    def _template_based_refinement(self, idea: str, knowledge: ProjectKnowledge) -> RefinedIdea:
        """Fallback template-based refinement when LLM is not available."""
        self.logger.info("Using template-based refinement")
        
        # Generate title
        title = idea.strip().rstrip('.')
        if len(title) > 80:
            title = title[:77] + "..."
        
        # Analyze idea for domain and components
        idea_lower = idea.lower()
        relevant_domain = self._identify_domain(idea_lower, knowledge.business_domains)
        relevant_components = self._identify_components(idea_lower, knowledge.technical_components)
        
        # Generate sections
        business_value = self._generate_business_value(idea, relevant_domain)
        acceptance_criteria = self._generate_acceptance_criteria(idea, relevant_domain)
        open_questions = self._generate_open_questions(idea, relevant_domain)
        suggested_design = self._generate_design_suggestions(idea, relevant_components)
        likely_files = self._predict_file_changes(idea, knowledge.file_change_patterns)
        impact_assessment = self._generate_impact_assessment(idea, relevant_components)
        related_tickets = self._find_related_tickets(idea, knowledge.tickets)
        
        description = self._build_description(
            business_value, acceptance_criteria, open_questions, 
            suggested_design, impact_assessment
        )
        
        return RefinedIdea(
            original_idea=idea,
            title=title,
            business_value=business_value,
            description=description,
            acceptance_criteria=acceptance_criteria,
            open_questions=open_questions,
            suggested_design=suggested_design,
            likely_files=likely_files,
            impact_assessment=impact_assessment,
            related_tickets=related_tickets
        )
    
    def _llm_based_refinement(self, idea: str, knowledge: ProjectKnowledge) -> RefinedIdea:
        """LLM-based refinement using the loaded model."""
        self.logger.info("Using LLM-based refinement")
        
        # Build context from knowledge
        context = self._build_llm_context(knowledge)
        
        # Create prompt
        prompt = self._create_refinement_prompt(idea, context)
        
        try:
            # Generate response
            sampler = make_sampler(temp=0.3, top_p=0.9)
            response = generate(
                self.model,
                self.tokenizer,
                prompt=prompt,
                max_tokens=2048,
                sampler=sampler,
                verbose=False
            )
            
            # Parse LLM response
            return self._parse_llm_response(idea, response)
            
        except Exception as e:
            self.logger.error(f"LLM refinement failed: {e}")
            return self._template_based_refinement(idea, knowledge)
    
    def _build_llm_context(self, knowledge: ProjectKnowledge) -> str:
        """Build context string for LLM from project knowledge."""
        context_parts = []
        
        # Add sample ticket patterns
        if knowledge.common_patterns.get('business_value'):
            context_parts.append("Example Business Values:")
            for bv in knowledge.common_patterns['business_value'][:3]:
                context_parts.append(f"- {bv}")
        
        # Add technical components
        if knowledge.technical_components:
            context_parts.append(f"\nTechnical Components: {', '.join(list(knowledge.technical_components)[:10])}")
        
        # Add business domains
        if knowledge.business_domains:
            context_parts.append(f"Business Domains: {', '.join(list(knowledge.business_domains))}")
        
        return "\n".join(context_parts)
    
    def _create_refinement_prompt(self, idea: str, context: str) -> str:
        """Create prompt for LLM refinement."""
        return f"""You are a senior technical product manager for a payment processing system. 
Your task is to refine a high-level idea into a comprehensive JIRA ticket description.

Project Context:
{context}

High-level idea to refine:
"{idea}"

Please provide a detailed ticket description with the following sections:
1. Title (concise, actionable)
2. Business Value (why this matters)
3. Acceptance Criteria (specific, testable requirements)
4. Open Questions (technical and business questions to resolve)
5. Suggested Design (high-level technical approach)
6. Impact Assessment (risks, dependencies, notifications needed)

Format your response as structured text with clear section headers.
"""
    
    def _parse_llm_response(self, idea: str, response: str) -> RefinedIdea:
        """Parse LLM response into RefinedIdea structure."""
        # This is a simplified parser - in practice you'd want more robust parsing
        sections = {}
        current_section = None
        current_content = []
        
        for line in response.split('\n'):
            line = line.strip()
            if not line:
                continue
                
            # Check for section headers
            if any(header in line.lower() for header in ['title:', 'business value:', 'acceptance criteria:', 'open questions:', 'suggested design:', 'impact assessment:']):
                if current_section:
                    sections[current_section] = '\n'.join(current_content)
                current_section = line.lower().split(':')[0].strip()
                current_content = []
            else:
                current_content.append(line)
        
        # Add final section
        if current_section:
            sections[current_section] = '\n'.join(current_content)
        
        # Extract structured data
        title = sections.get('title', idea)
        business_value = sections.get('business value', 'To be determined')
        acceptance_criteria = [sections.get('acceptance criteria', 'To be defined')]
        open_questions = [sections.get('open questions', 'To be identified')]
        suggested_design = sections.get('suggested design', 'To be designed')
        impact_assessment = sections.get('impact assessment', 'To be assessed')
        
        description = self._build_description(
            business_value, acceptance_criteria, open_questions,
            suggested_design, impact_assessment
        )
        
        return RefinedIdea(
            original_idea=idea,
            title=title,
            business_value=business_value,
            description=description,
            acceptance_criteria=acceptance_criteria,
            open_questions=open_questions,
            suggested_design=suggested_design,
            likely_files=[],  # Would need additional analysis
            impact_assessment=impact_assessment,
            related_tickets=[]  # Would need additional analysis
        )
    
    def _identify_domain(self, idea_lower: str, domains: Set[str]) -> str:
        """Identify the most relevant business domain for the idea."""
        domain_keywords = {
            'authorization': ['auth', 'authorization', 'approve', 'decline'],
            'clearing': ['clearing', 'settlement', 'reconcile'],
            'fraud_prevention': ['fraud', 'risk', 'security', 'detect'],
            'card_schemes': ['visa', 'mastercard', 'mc', 'scheme'],
            'merchant_services': ['merchant', 'customer', 'dashboard'],
            'terminal_management': ['terminal', 'pos', 'device']
        }
        
        for domain, keywords in domain_keywords.items():
            if any(keyword in idea_lower for keyword in keywords):
                return domain
        
        return 'general'

    def _identify_components(self, idea_lower: str, components: Set[str]) -> List[str]:
        """Identify relevant technical components for the idea."""
        component_keywords = {
            'auth_services': ['auth', 'authorization', 'approve'],
            'clearing_services': ['clearing', 'settlement'],
            'kubernetes': ['deploy', 'scale', 'infrastructure'],
            'configuration': ['config', 'setting', 'parameter'],
            'testing': ['test', 'validation', 'verify'],
            'internal_apis': ['api', 'endpoint', 'service'],
            'applications': ['app', 'application', 'service']
        }

        relevant = []
        for component, keywords in component_keywords.items():
            if any(keyword in idea_lower for keyword in keywords):
                relevant.append(component)

        return relevant

    def _generate_business_value(self, idea: str, domain: str) -> str:
        """Generate business value statement."""
        domain_values = {
            'authorization': 'Improves payment authorization accuracy and reduces false declines',
            'clearing': 'Streamlines settlement processes and reduces operational overhead',
            'fraud_prevention': 'Reduces fraud losses and improves customer trust',
            'card_schemes': 'Ensures compliance and improves scheme relationships',
            'merchant_services': 'Enhances merchant experience and reduces support burden',
            'terminal_management': 'Improves terminal efficiency and reduces downtime',
            'general': 'Enhances system capabilities and operational efficiency'
        }

        base_value = domain_values.get(domain, domain_values['general'])
        return f"{base_value}. {idea}"

    def _generate_acceptance_criteria(self, idea: str, domain: str) -> List[str]:
        """Generate acceptance criteria based on idea and domain."""
        criteria = [
            "Feature is implemented and tested",
            "Documentation is updated",
            "No regression in existing functionality"
        ]

        domain_criteria = {
            'authorization': [
                "Authorization success rate maintained or improved",
                "Response time within SLA requirements",
                "Proper error handling and logging"
            ],
            'clearing': [
                "Settlement files generated correctly",
                "Reconciliation processes updated",
                "Audit trail maintained"
            ],
            'fraud_prevention': [
                "False positive rate minimized",
                "Detection accuracy meets requirements",
                "Performance impact assessed"
            ]
        }

        if domain in domain_criteria:
            criteria.extend(domain_criteria[domain])

        return criteria

    def _generate_open_questions(self, idea: str, domain: str) -> List[str]:
        """Generate open questions to be resolved."""
        questions = [
            "What are the performance requirements?",
            "Are there any compliance considerations?",
            "What is the rollback strategy?"
        ]

        domain_questions = {
            'authorization': [
                "How will this affect existing authorization flows?",
                "What scheme-specific requirements need consideration?",
                "How will we handle backward compatibility?"
            ],
            'clearing': [
                "How will this impact settlement timelines?",
                "Are there regulatory reporting implications?",
                "How will we handle data migration?"
            ],
            'fraud_prevention': [
                "What is the acceptable false positive rate?",
                "How will we train and validate models?",
                "What data privacy considerations apply?"
            ]
        }

        if domain in domain_questions:
            questions.extend(domain_questions[domain])

        return questions

    def _generate_design_suggestions(self, idea: str, components: List[str]) -> str:
        """Generate high-level design suggestions."""
        suggestions = []

        if 'auth_services' in components:
            suggestions.append("Consider impact on existing auth services and APIs")
        if 'clearing_services' in components:
            suggestions.append("Ensure integration with clearing and settlement processes")
        if 'kubernetes' in components:
            suggestions.append("Plan for containerized deployment and scaling")
        if 'internal_apis' in components:
            suggestions.append("Design RESTful APIs with proper versioning")

        if not suggestions:
            suggestions.append("Design should follow existing architectural patterns")

        suggestions.extend([
            "Implement proper error handling and logging",
            "Consider monitoring and alerting requirements",
            "Plan for gradual rollout and feature flags"
        ])

        return ". ".join(suggestions) + "."

    def _predict_file_changes(self, idea: str, file_patterns: Dict[str, List[str]]) -> List[str]:
        """Predict likely file changes based on idea content."""
        idea_lower = idea.lower()
        likely_files = []

        # Map idea keywords to file pattern categories
        if any(word in idea_lower for word in ['auth', 'authorization']):
            likely_files.extend(file_patterns.get('auth', [])[:5])
        if any(word in idea_lower for word in ['clearing', 'settlement']):
            likely_files.extend(file_patterns.get('clearing', [])[:5])
        if any(word in idea_lower for word in ['api', 'endpoint']):
            likely_files.extend(file_patterns.get('api', [])[:5])
        if any(word in idea_lower for word in ['test', 'testing']):
            likely_files.extend(file_patterns.get('testing', [])[:5])
        if any(word in idea_lower for word in ['config', 'configuration']):
            likely_files.extend(file_patterns.get('config', [])[:5])

        # Remove duplicates while preserving order
        seen = set()
        unique_files = []
        for file in likely_files:
            if file not in seen:
                seen.add(file)
                unique_files.append(file)

        return unique_files[:10]  # Limit to top 10 predictions

    def _generate_impact_assessment(self, idea: str, components: List[str]) -> str:
        """Generate impact assessment."""
        impacts = []

        if any(comp in components for comp in ['auth_services', 'clearing_services']):
            impacts.append("Could affect critical payment processing - requires careful testing")

        if 'kubernetes' in components:
            impacts.append("May require infrastructure changes and deployment coordination")

        if not impacts:
            impacts.append("Low to medium impact expected")

        impacts.extend([
            "Notify relevant teams before implementation",
            "Plan for monitoring during rollout",
            "Ensure rollback procedures are in place"
        ])

        return ". ".join(impacts) + "."

    def _find_related_tickets(self, idea: str, tickets: List[Dict]) -> List[str]:
        """Find related tickets based on content similarity."""
        idea_words = set(idea.lower().split())
        related = []

        for ticket in tickets[:100]:  # Limit search for performance
            title = ticket.get('title', '').lower()
            title_words = set(title.split())

            # Simple word overlap scoring
            overlap = len(idea_words.intersection(title_words))
            if overlap >= 2:  # At least 2 words in common
                related.append(f"{ticket.get('key', 'UNKNOWN')}: {ticket.get('title', 'No title')}")

        return related[:5]  # Return top 5 related tickets

    def _build_description(self, business_value: str, acceptance_criteria: List[str],
                          open_questions: List[str], suggested_design: str,
                          impact_assessment: str) -> str:
        """Build the complete ticket description."""
        description_parts = [
            f"*Business Value:*\n{business_value}\n",
            f"*Acceptance Criteria:*"
        ]

        for i, criteria in enumerate(acceptance_criteria, 1):
            description_parts.append(f"* {criteria}")

        description_parts.extend([
            f"\n*Open Questions:*"
        ])

        for i, question in enumerate(open_questions, 1):
            description_parts.append(f"* {question}")

        description_parts.extend([
            f"\n*Suggested Design:*\n{suggested_design}\n",
            f"*Impact and Notifications:*\n{impact_assessment}"
        ])

        return "\n".join(description_parts)


class LLMIdeaRefinerApp:
    """Main application class using LLM-powered refinement with debug logging."""

    def __init__(self, model_preset: str = "qwen3", debug_dir: Optional[Path] = None,
                 debug_enabled: bool = True):
        self.logger = logging.getLogger(__name__)
        self.knowledge_extractor = SmartKnowledgeExtractor()

        if not MLX_AVAILABLE:
            self.logger.error("This version requires MLX for LLM-based refinement")
            raise RuntimeError("MLX required - install with: pip install mlx-lm")

        # Initialize debug logging
        self.debug_logger = LLMDebugLogger(debug_dir, debug_enabled)

        # Initialize LLM refiner with debug logger
        self.llm_refiner = IntelligentLLMRefiner(model_preset, self.debug_logger)

    def refine_ideas_from_file(self, ideas_path: Path, jira_path: Path,
                              enriched_path: Path, tree_path: Path,
                              git_path: Path, output_dir: Path, args: argparse.Namespace) -> None:
        """Refine all ideas from a file and generate ticket descriptions with debug logging."""

        # Log run start
        self.debug_logger.log_run_start(args, self.llm_refiner.model_preset)

        try:
            # Extract project knowledge
            knowledge = self.knowledge_extractor.extract_knowledge(
                jira_path, enriched_path, tree_path, git_path
            )

            # Load ideas
            ideas = self._load_ideas(ideas_path)
            self.logger.info(f"Loaded {len(ideas)} ideas to refine")

            # Create output directory
            output_dir.mkdir(parents=True, exist_ok=True)

            # Process each idea
            for i, idea in enumerate(ideas, 1):
                self.logger.info(f"Processing idea {i}/{len(ideas)}: {idea[:50]}...")

                try:
                    refined = self.llm_refiner.refine_idea(idea, knowledge, idea_num=i)

                    # Generate output filename
                    safe_title = self._make_safe_filename(refined.title)
                    output_file = output_dir / f"ticket_{i:02d}_{safe_title}.txt"

                    # Write ticket description
                    self._write_ticket_file(refined, output_file)

                    self.logger.info(f"Generated ticket: {output_file}")

                except Exception as e:
                    self.logger.error(f"Failed to process idea {i}: {e}")
                    continue

            self.logger.info(f"Completed processing {len(ideas)} ideas")

        finally:
            # Finalize debug logging
            self.debug_logger.finalize_run()

    def _load_ideas(self, ideas_path: Path) -> List[str]:
        """Load ideas from text file."""
        try:
            with open(ideas_path, 'r', encoding='utf-8') as f:
                lines = [line.strip() for line in f if line.strip()]

            # Handle numbered lists
            ideas = []
            for line in lines:
                # Remove leading numbers and dots
                clean_line = re.sub(r'^\d+\.\s*', '', line)
                if clean_line:
                    ideas.append(clean_line)

            return ideas

        except Exception as e:
            self.logger.error(f"Failed to load ideas from {ideas_path}: {e}")
            return []

    def _make_safe_filename(self, title: str) -> str:
        """Convert title to safe filename."""
        # Remove/replace unsafe characters
        safe = re.sub(r'[^\w\s-]', '', title)
        safe = re.sub(r'[-\s]+', '_', safe)
        return safe.lower()[:50]  # Limit length

    def _write_ticket_file(self, refined: RefinedIdea, output_file: Path) -> None:
        """Write refined idea to ticket file."""
        content_parts = [
            f"# {refined.title}",
            "",
            f"**Original Idea:** {refined.original_idea}",
            "",
            refined.description,
            ""
        ]

        if refined.likely_files:
            content_parts.extend([
                "*Likely Files to Modify:*",
                ""
            ])
            for file_path in refined.likely_files:
                content_parts.append(f"* {file_path}")
            content_parts.append("")

        if refined.related_tickets:
            content_parts.extend([
                "*Related Tickets:*",
                ""
            ])
            for ticket in refined.related_tickets:
                content_parts.append(f"* {ticket}")
            content_parts.append("")

        content_parts.extend([
            "*Refined By:* Idea Refiner Script",
            "",
            f"*Dependencies:* To be determined",
            "",
            f"*Out of Scope:* To be determined"
        ])

        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("\n".join(content_parts))


def parse_args() -> argparse.Namespace:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Refine high-level ideas into comprehensive JIRA ticket descriptions",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    parser.add_argument(
        "--ideas",
        type=Path,
        required=True,
        help="Text file containing ideas (one per line or numbered list)"
    )

    parser.add_argument(
        "--jira_tickets",
        type=Path,
        required=True,
        help="JSON file with historical JIRA tickets"
    )

    parser.add_argument(
        "--enriched_tickets",
        type=Path,
        required=True,
        help="JSON file with enriched JIRA tickets (with file changes)"
    )

    parser.add_argument(
        "--tree_log",
        type=Path,
        required=True,
        help="Text file with project structure (tree output)"
    )

    parser.add_argument(
        "--git_log",
        type=Path,
        required=True,
        help="Text file with git commit history"
    )

    parser.add_argument(
        "--output_dir",
        type=Path,
        default=Path("refined_tickets"),
        help="Directory to write refined ticket files"
    )

    parser.add_argument(
        "--model",
        choices=list(PRESETS.keys()),
        default="qwen3",
        help="LLM model preset to use for refinement"
    )

    parser.add_argument(
        "--log_level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level"
    )

    parser.add_argument(
        "--debug_dir",
        type=Path,
        default=Path("llm_debug_logs"),
        help="Directory for debug logs (creates timestamped subdirectories)"
    )

    parser.add_argument(
        "--no_debug_logs",
        action="store_true",
        help="Disable debug logging to save disk space"
    )

    return parser.parse_args()


def main():
    """Main entry point."""
    args = parse_args()

    # Set up logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )

    logger = logging.getLogger(__name__)

    # Validate input files
    for file_path in [args.ideas, args.jira_tickets, args.enriched_tickets,
                      args.tree_log, args.git_log]:
        if not file_path.exists():
            logger.error(f"Input file not found: {file_path}")
            sys.exit(1)

    # Check MLX availability if using LLM
    if not MLX_AVAILABLE:
        logger.warning("MLX not available - using template-based refinement only")

    try:
        # Create and run the LLM-powered refiner with debug logging
        debug_enabled = not args.no_debug_logs
        app = LLMIdeaRefinerApp(
            model_preset=args.model,
            debug_dir=args.debug_dir,
            debug_enabled=debug_enabled
        )

        app.refine_ideas_from_file(
            args.ideas, args.jira_tickets, args.enriched_tickets,
            args.tree_log, args.git_log, args.output_dir, args
        )

        logger.info(f"LLM-powered refinement complete. Check output in: {args.output_dir}")
        if debug_enabled:
            logger.info(f"Debug logs available in: {args.debug_dir}")

    except Exception as e:
        logger.error(f"Refinement failed: {e}")
        if "MLX" in str(e):
            logger.error("Install MLX with: pip install mlx-lm")
        sys.exit(1)


if __name__ == "__main__":
    main()
