# Migrate Clearing Instructions from SQS to Kafka

**Original Idea:** Clearing instructions are to be sent via Kafka and not SQS

*Business Value:*
Improves message delivery reliability and compliance with scheme-specific protocols

*Acceptance Criteria:*
* Clearing instructions must be sent via Kafka with 99.9% reliability SLA
* Mastercard and Visa must support Kafka-based message routing without format changes
* All messages must be validated against schema in internal/scheme/mastercard/converters_auth_responses.go

*Open Questions:*
* What are the specific Kafka topic configurations for each scheme?
* What is the required message format for JCB in Kafka compared to SQS?
* Does the migration require additional certification for Amex?

*Technical Design:*
Replace SQS publisher in internal/operation/operations_requests.go with Kafka producer. Implement schema validation using existing converters in internal/scheme/mastercard/converters_auth_responses.go. Ensure all messages include DE11 (STAN) and DE12 (transaction time) for reconciliation. Use Kafka's idempotent producer to prevent duplicates.

*Impact and Notifications:*
Dependencies on Kafka cluster availability; must coordinate with infrastructure team. Notify all scheme partners of protocol changes. Risk of message delay during migration window.

*Likely Files to Modify:*

* internal/operation/operations_requests.go
* internal/scheme/mastercard/converters_auth_responses.go
* internal/scheme/jcb/converters_events_test.go

*Related Tickets:*

* MARS-1933: dashboards are not reacting to scheme dropdown change
* MARS-2199: Investigate Ecom transactions which are being rejected in Clearing.
* MARS-2622: MC transactions failed to be stored

*Refined By:* Idea Refiner Script

*Dependencies:* To be determined

*Out of Scope:* To be determined