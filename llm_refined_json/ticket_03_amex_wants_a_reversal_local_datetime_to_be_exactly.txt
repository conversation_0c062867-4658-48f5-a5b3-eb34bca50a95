# Amex wants a reversal local datetime to be exactly like the time on a terminal

**Original Idea:** Amex wants a reversal local datetime to be exactly like the time on a terminal

*Business Value:*
Business value to be determined

*Acceptance Criteria:*

*Open Questions:*

*Technical Design:*
Technical design to be determined

*Impact and Notifications:*
Impact assessment to be determined

*Related Tickets:*

* MARS-2745: use correct reversal reason values for MC
* MARS-2400: AMEX tech debt
* MARS-2507: Local date and time in DF12 should be in the same timezone for auth and reversals 

*Refined By:* Idea Refiner Script

*Dependencies:* To be determined

*Out of Scope:* To be determined