# Squash all our DB migrations into a simpler big one

**Original Idea:** Squash all our DB migrations into a simpler big one

*Business Value:*
Business value to be determined

*Acceptance Criteria:*

*Open Questions:*

*Technical Design:*
Technical design to be determined

*Impact and Notifications:*
Impact assessment to be determined

*Refined By:* Idea Refiner Script

*Dependencies:* To be determined

*Out of Scope:* To be determined