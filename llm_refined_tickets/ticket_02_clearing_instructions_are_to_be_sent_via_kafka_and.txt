# Clearing instructions are to be sent via Kafka and not SQS

**Original Idea:** Clearing instructions are to be sent via Kafka and not SQS

*Business Value:*
Business value to be determined

*Acceptance Criteria:*

*Open Questions:*

*Technical Design:*
Technical design to be determined

*Impact and Notifications:*
Impact assessment to be determined

*Related Tickets:*

* MARS-1933: dashboards are not reacting to scheme dropdown change
* MARS-2199: Investigate Ecom transactions which are being rejected in Clearing.
* MARS-2622: MC transactions failed to be stored

*Refined By:* Idea Refiner Script

*Dependencies:* To be determined

*Out of Scope:* To be determined