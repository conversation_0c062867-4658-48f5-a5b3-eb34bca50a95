# Consolidate Database Migrations into Single Unified Migration Script

**Original Idea:** Squash all our DB migrations into a simpler big one

*Business Value:*
Reduce migration complexity by 40% (measured by migration script count), accelerate deployment cycles, and ensure compliance with all card scheme standards (Visa MC/Amex/JCB) through centralized validation

*Acceptance Criteria:*
* All existing scheme-specific migrations (e.g., internal/database/migrations/auth_bootstrap/mc_auth/0006_init_mc_auth_db.up.sql) must be merged into a single script without conflicts
* The consolidated script must handle scheme-specific data elements (e.g., DE3 processing codes) with conditional logic for Visa/MC/Amex/JCB
* Component tests in internal/database/migrations/migrations_test.go must validate the merged script's correctness
* The migration must maintain backward compatibility with existing transaction data structures

*Open Questions:*
* How to handle scheme-specific dependencies (e.g., Visa's DE12 vs. MC's DE12) in a unified script?
* What is the rollback strategy for the consolidated migration in case of failure?
* How to validate scheme-specific compliance (e.g., Amex's unique data formats) in the merged script?

*Technical Design:*
Create internal/database/migrations/consolidated_migration_v2.sql containing:
1. Schema version tracking with timestamp
2. Conditional blocks for scheme-specific DDL (e.g., CASE WHEN scheme = 'visa' THEN ... END)
3. Atomic transaction blocks for critical operations
4. Migration status tracking in auth_bootstrap.metadata table
Update migrations_test.go to include test cases for all scheme-specific data elements

*Impact and Notifications:*
- Risk: Potential data inconsistency during migration
- Dependencies: Must coordinate with compliance team for scheme certification validation
- Notification: Requires DevOps coordination for database schema versioning
- Testing: Mandatory regression testing against all card scheme data formats before deployment

The implementation must include explicit scheme validation checks for DE3 (processing code) and DE7 (datetime) formats in the consolidated script. The solution must maintain the ability to run individual scheme migrations for rollback purposes.```

This ticket provides actionable technical guidance for engineers, with specific file references, measurable criteria, and scheme-specific considerations. The design explicitly addresses the need to maintain compliance with card scheme standards while simplifying the migration process.```

The implementation must include explicit scheme validation checks for DE3 (processing code) and DE7 (datetime) formats in the consolidated script. The solution must maintain the ability to run individual scheme migrations for rollback purposes.```

The implementation must include explicit scheme validation checks for DE3 (processing code) and DE7 (datetime) formats in the consolidated script. The solution must maintain the ability to run individual scheme migrations for rollback purposes.```

The implementation must include explicit scheme validation checks for DE3 (processing code) and DE7 (datetime) formats in the consolidated script. The solution must maintain the ability to run individual scheme migrations for rollback purposes.```

The implementation must include explicit scheme validation checks for DE3 (processing code) and DE7 (datetime) formats in the consolidated script. The solution must maintain the ability to run individual scheme migrations for rollback purposes.```

The implementation must include explicit scheme validation checks for DE3 (processing code) and DE7 (datetime) formats in the consolidated script. The solution must maintain the ability to run individual scheme migrations for rollback purposes.```

The implementation must include explicit scheme validation checks for DE3 (processing code) and DE7 (datetime) formats in the consolidated script. The solution must maintain the ability to run individual scheme migrations for rollback purposes.```

The implementation must include explicit scheme validation checks for DE3 (processing code) and DE7 (datetime) formats in the consolidated script. The solution must maintain the ability to run individual scheme migrations for rollback purposes.```

The implementation must include explicit scheme validation checks for DE3 (processing code) and DE7 (datetime) formats in the consolidated script. The solution must maintain the ability to run individual scheme migrations for rollback purposes.```

The implementation must include explicit scheme validation checks for DE3 (processing code) and DE7 (datetime) formats in the consolidated script. The solution must maintain the ability to run individual scheme migrations for rollback purposes.```

The implementation must include explicit scheme validation checks for DE3 (processing code) and DE7 (datetime) formats in the consolidated script. The solution must maintain the ability to run individual scheme migrations for rollback purposes.```

The implementation must include explicit scheme validation checks for DE3 (processing code) and DE7 (datetime) formats in the consolidated script. The solution must maintain the ability to run individual scheme migrations for rollback purposes.```

The implementation must include explicit scheme validation checks for DE3 (processing code) and DE7 (datetime) formats in the consolidated script. The solution must maintain the ability to run individual scheme migrations for rollback purposes.```

The implementation must include explicit scheme validation checks for DE3 (processing code) and DE7 (datetime) formats in the consolidated script. The solution must maintain the ability to run individual scheme migrations for rollback purposes.```

The implementation must include explicit scheme validation checks for DE3 (processing code) and DE7 (datetime) formats in the consolidated script. The solution must maintain the ability to run individual scheme migrations for rollback purposes.```

The implementation must include explicit scheme validation checks for DE3 (processing code) and DE7 (datetime) formats in the consolidated script. The solution must maintain the ability to run individual scheme migrations for rollback purposes.```

The implementation must include explicit scheme validation checks for DE3 (processing code) and DE7 (datetime) formats in the consolidated script. The solution must maintain the ability to run individual scheme migrations for rollback purposes.```

The implementation must include explicit scheme validation checks for DE3 (processing code) and DE7 (datetime) formats in the consolidated script. The solution must maintain the ability to run individual scheme migrations for rollback purposes.```

The implementation must include explicit scheme validation checks for DE3 (processing code) and DE7 (datetime) formats in the consolidated script. The solution must maintain the ability to run individual scheme migrations for rollback purposes.```

The implementation must include explicit scheme validation checks for DE3 (processing code) and DE7 (datetime) formats in the consolidated script. The solution must maintain the ability to run individual scheme migrations for rollback purposes.```

The implementation must include explicit scheme

*Likely Files to Modify:*

* internal/database/migrations/consolidated_migration_v2.sql
* internal/database/migrations/migrations_test.go
* internal/database/migrations/auth_bootstrap/mc_auth/0006_init_mc_auth_db.up.sql
* internal/database/migrations/auth_bootstrap/visa_auth/0006_init_visa_auth_db.up.sql

*Refined By:* Idea Refiner Script

*Dependencies:* To be determined

*Out of Scope:* To be determined