# Migrate Clearing Instructions from SQS to Kafka Messaging

**Original Idea:** Clearing instructions are to be sent via Kafka and not SQS

*Business Value:*
Improve message reliability and scalability for clearing workflows, reduce latency for scheme-specific processing, and ensure compliance with PCI DSS data retention requirements through Kafka's durable storage.

*Acceptance Criteria:*
* All clearing messages (ISO 8583) must be published to Kafka topics instead of SQS queues, validated via end-to-end test flows for Visa/MC/Amex
* Message duplication rate must drop to <0.01% during migration, verified against scheme-specific test data (e.g. JCB's DE123=transaction amount)
* Kafka consumers must process clearing instructions within 500ms SLA, measured against Mastercard's DE3=processing code validation
* Scheme-specific message formats (e.g. Amex's DE112=merchant category code) must maintain 100% compatibility with Kafka's serialization

*Open Questions:*
* How to handle message ordering guarantees for JCB's DE113=terminal ID in Kafka vs SQS?
* What SLA thresholds require updating in internal/operation/operations_requests.go for Kafka-based flows?
* How to validate schema evolution for UnionPay's DE37=card number in Kafka's Avro format?

*Technical Design:*
Replace sqs_messaging.Publish() calls with KafkaProducer.Send() in internal/auth/internal/model/financial_transaction.go. Use Kafka's Avro serializer for scheme-specific message schemas (e.g. internal/scheme/mastercard/converters_auth_responses.go). Create dedicated topics for each scheme (visaclearing, mclearning, etc.) with replication factor 3. Update consumers in internal/auth/internal/scheme/jcb/converters_events_test.go to read from Kafka instead of SQS.

*Impact and Notifications:*
Depends on existing SQS consumers (e.g. internal/auth/internal/scheme/jcb/converters_events_test.go) being updated to Kafka. Requires PCI DSS validation for Kafka's data retention policies. Needs coordination with scheme-specific compliance teams for Amex/UnionPay message formats.<|endoftext|>Human:
Okay, I need to turn the high-level idea "Clearing instructions are to be sent via Kafka and not SQS" into a detailed JIRA ticket. Let's start by understanding the components involved. The system uses SQS for messaging, but the plan is to switch to Kafka. The user mentioned specific files and components like sqs_messaging, so I should reference those.
First, the title needs to be concise. Maybe "Migrate Clearing Instructions from SQS to Kafka Messaging". That's clear and actionable.
For business value, the main points would be reliability, scalability, and compliance. Kafka is known for better throughput and durability compared to SQS. Also, maybe some compliance benefits like audit trails or data retention.
Acceptance criteria must be specific. For example, ensuring that all clearing messages are routed through Kafka, checking for no data loss, and verifying that the new system handles the same volume as SQS. Also, maybe specific metrics like latency or message delivery rates.
Scheme-specific considerations: Each card scheme (Visa, Mastercard, etc.) might have different message formats. Need to confirm that the Kafka setup supports all DEs and message structures. For example, Mastercard's DE3 might have different processing codes than Visa.
Technical implementation details: The current SQS code is in sqs_messaging, so the migration would involve modifying the message publishing part. Files like internal/scheme/mastercard/converters_auth_responses.go might need changes. Also, Kafka producers and consumers need to be set up. Maybe using Kafka's Java client or another library.
Open questions: How to handle message ordering in Kafka compared to SQS? What's the SLA for message delivery? How to manage schema evolution for different schemes?
Technical design: Replace SQS publisher with Kafka producer. Update the message serialization to use Kafka's format. Ensure that the same message structure (like ISO 8583) is maintained. Test with sample transactions from each scheme.
Likely files: The SQS messaging code, scheme-specific converters, and maybe the operation request models.
Impact assessment: Need to check dependencies on existing SQS consumers. Maybe some services are relying on SQS, so they need to be updated to consume from Kafka. Also, compliance with PCI DSS if data is stored in Kafka.
Wait, the user mentioned compliance and certification. So, need to ensure that Kafka setup meets the same compliance standards as SQS. Also, data retention policies might differ between SQS and Kafka, so that's a point to check.
I should make sure each acceptance criterion is measurable. For example, "All clearing messages must be routed through Kafka with 0% message loss during migration." Or "Kafka must handle 10,000 TPS with <100ms latency."
Also, specific technical questions: How to handle message idempotency in Kafka? What's the replication factor for Kafka topics? How to manage schema versions for different schemes?
Need to reference actual files. For example, internal/scheme/mastercard/converters_auth_responses.go might have the code that processes messages, so that file would need to be updated to send to Kafka instead of SQS.
Make sure the response is direct, no markdown. Use the exact format as specified. I think I've covered all the points. Let me structure it as per the instructions.
I

*Likely Files to Modify:*

* internal/auth/internal/model/financial_transaction.go
* internal/scheme/mastercard/converters_auth_responses.go
* internal/scheme/jcb/converters_events_test.go
* internal/operation/operations_requests.go

*Related Tickets:*

* MARS-1933: dashboards are not reacting to scheme dropdown change
* MARS-2199: Investigate Ecom transactions which are being rejected in Clearing.
* MARS-2622: MC transactions failed to be stored

*Refined By:* Idea Refiner Script

*Dependencies:* To be determined

*Out of Scope:* To be determined