# Ensure Amex reversal messages use terminal's local datetime exactly as in original authorization

**Original Idea:** Amex wants a reversal local datetime to be exactly like the time on a terminal

*Business Value:*
Ensures compliance with Amex's certification requirements by maintaining exact datetime alignment between original transactions and reversals. Prevents reconciliation discrepancies and certification failures in Amex's test environments.

*Acceptance Criteria:*
* Amex reversal messages (ISO 8583) must have DE7 (processing time) exactly matching the original authorization's terminal local datetime (to the second)
* datetime format in DE7 must follow Amex's specification (YYMMDDHHMMSS in local timezone)
* Component tests in componenttest/amexapi/authorize_3ds_test.go must validate reversal datetime matches original transaction's terminal time
* Reversal requests in internal/auth/internal/scheme/amex/converters_reversal_requests.go must source datetime from original transaction's terminal time field

*Open Questions:*
* Where is the terminal's local datetime stored in the original transaction record? (e.g., which DB field or message DE?)
* What specific Amex certification test cases (e.g., in componenttest/amexapi/certification_test.go) need to validate this behavior?
* How does Amex's test environment validate datetime consistency between auth and reversal?

*Technical Design:*
1. Modify internal/auth/internal/scheme/amex/converters_reversal_requests.go to source datetime from original transaction's terminal_time field (likely DE11 or similar)
2. Ensure datetime formatting uses Amex's required format (YYMMDDHHMMSS) with local timezone
3. Update componenttest/amexapi/authorize_3ds_test.go to:
- Create test transactions with known terminal times
- Generate reversals and verify DE7 matches exactly
4. Add validation in internal/auth/internal/scheme/amex/validators.go to check datetime consistency

*Impact and Notifications:*
- Risk: Incorrect datetime could cause Amex certification failures or reconciliation errors
- Dependency: Requires access to Amex's certification test environment for validation
- Notification: Engineering team needs to coordinate with compliance team to verify datetime format requirements
- Regression risk: Existing reversal flows may need to be audited to ensure they don't accidentally modify datetime values
The engineer should start by locating where terminal time is stored in the transaction record, then modify the reversal converter to use that value. Test cases in the Amex component tests must be updated to verify the exact datetime match. Need to confirm Amex's specific datetime format requirements and certification test scenarios.

*Likely Files to Modify:*

* internal/auth/internal/scheme/amex/converters_reversal_requests.go
* componenttest/amexapi/authorize_3ds_test.go
* internal/auth/internal/scheme/amex/validators.go
* internal/auth/internal/models/transaction.go (where terminal time is stored)

*Related Tickets:*

* MARS-2745: use correct reversal reason values for MC
* MARS-2400: AMEX tech debt
* MARS-2507: Local date and time in DF12 should be in the same timezone for auth and reversals 

*Refined By:* Idea Refiner Script

*Dependencies:* To be determined

*Out of Scope:* To be determined