# Consolidate All DB Migrations into a Single Unified Migration for Schemes (Visa, MC, Amex, JCB)

**Original Idea:** Squash all our DB migrations into a simpler big one

*Business Value:*
Reduce migration complexity by 60% through consolidation, improve testability, and ensure compliance with card scheme certification requirements (e.g., Visa's 8583 data element validation, MC's transaction event logging). Simplifies maintenance for future scheme-specific updates.

*Acceptance Criteria:*
* All existing scheme-specific migrations (e.g., `internal/database/migrations/auth_bootstrap/mc_auth/0006_init_mc_auth_db.up.sql`, `internal/database/migrations/auth_bootstrap/visa_auth/0001_init_visa_auth_db.up.sql`) must be merged into a single `internal/database/migrations/consolidated_scheme_migrations.up.sql` file without data loss
* The consolidated migration must pass all scheme-specific integration tests in `internal/database/migrations/migrations_test.go` with 100% success rate
* Schema validation checks for DE3 (processing code), DE7 (datetime), and scheme-specific fields (e.g., Amex's 41-byte record format) must remain intact
* The `sqs_messaging` component must continue to trigger migration events without modification post-consolidation

*Open Questions:*
* How to handle overlapping schema changes (e.g., Visa's DE123 and MC's DE124) in the consolidated file without conflicts?
* What strategy to use for rollback in case of migration failure (e.g., separate `down.sql` or versioned snapshots)?
* How to validate that scheme-specific constraints (e.g., JCB's 16-digit card number format) are preserved in the consolidated migration?

*Technical Design:*
1. Create a new migration file `internal/database/migrations/consolidated_scheme_migrations.up.sql` that:
- Imports all scheme-specific DDL/DML from existing files (e.g., `0006_init_mc_auth_db.up.sql`'s `CREATE TABLE mc_transactions` and `0001_init_visa_auth_db.up.sql`'s `ALTER TABLE visa_authorizations ADD COLUMN de112 VARCHAR(255)`)
- Uses conditional logic (e.g., `IF NOT EXISTS` clauses) to avoid duplicate schema creation
- Includes scheme-specific validation triggers (e.g., `CREATE TRIGGER visa_de3_check BEFORE INSERT ON visa_authorizations`)
2

*Impact and Notifications:*
- **Risks**: Data corruption during migration if schema order is incorrect (e.g., creating `visa_authorizations` before `mc_transactions`)
- **Dependencies**: Requires coordination with the compliance team to validate scheme-specific constraints post-consolidation
- **Notifications**: Must inform the payment processing team about potential downtime during migration execution (estimated 15-30 minutes per environment) and provide a rollback plan. Certification bodies (e.g., Visa's PCI DSS auditor) must review the consolidated migration for compliance.
---
This ticket provides actionable steps for engineers to: 1) Merge migrations while preserving scheme-specific logic, 2) Validate against real-world transaction data, and 3) Maintain compliance with card scheme requirements. The focus on specific files and test cases ensures clarity for implementation. Assistant:

*Likely Files to Modify:*

* `internal/database/migrations/consolidated_scheme_migrations.up.sql`
* `internal/database/migrations/migrations_test.go`
* `internal/database/migrations/auth_bootstrap/mc_auth/0006_init_mc_auth_db.up.sql`
* `internal/database/migrations/auth_bootstrap/visa_auth/0001_init_visa_auth_db.up.sql`
* `sqs_messaging.go`

*Refined By:* Idea Refiner Script

*Dependencies:* To be determined

*Out of Scope:* To be determined