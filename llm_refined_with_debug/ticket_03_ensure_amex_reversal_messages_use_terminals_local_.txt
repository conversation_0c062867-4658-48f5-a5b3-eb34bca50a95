# Ensure Amex reversal messages use terminal's local datetime for DE12

**Original Idea:** Amex wants a reversal local datetime to be exactly like the time on a terminal

*Business Value:*
Ensures compliance with Amex certification requirements and enables accurate reconciliation by matching terminal timestamps in reversal transactions.

*Acceptance Criteria:*
* DE12 (local date/time) in Amex reversal messages must match the terminal's local time exactly as captured during authorization
* Amex reversal requests must use the same datetime format (YYYYMMDDHHMMSS) as terminal timestamps
* Component tests in componenttest/amexapi/authorize_3ds_test.go must validate datetime consistency between authorization and reversal messages
* The system must preserve terminal timezone information in reversal messages for Amex

*Open Questions:*
* How is terminal time currently captured in authorization messages for Amex?
* What timezone conversion rules apply when terminal time is in a different timezone than the processing system?
* Does Amex certification require specific datetime format validation beyond ISO 8583 standards?

*Technical Design:*
Update the Amex reversal message builder to:
1. Retrieve terminal datetime from authorization response (DE12)
2. Use the same datetime value in reversal messages without modification
3. Validate datetime format matches ISO 8583's 10-byte local date/time format
4. Modify internal/auth/internal/scheme/amex/mappers.go to enforce this behavior
5. Add verification in componenttest/amexapi/request_v2.go to compare datetime values

*Impact and Notifications:*
- Requires regression testing of all Amex reversal flows
- May require updates to reconciliation systems that compare transaction timestamps
- Certification team must validate datetime consistency with Amex's test environment
- Potential for data format issues if terminal time uses non-standard timezone offsets
- All changes must be reflected in the system's ISO 8583 message specification documentation.

*Likely Files to Modify:*

* internal/auth/internal/scheme/amex/mappers.go
* componenttest/amexapi/request_v2.go
* internal/auth/internal/scheme/amex/converters_reversal_requests.go
* internal/auth/internal/scheme/amex/converters_reversal_requests_test.go

*Related Tickets:*

* MARS-2745: use correct reversal reason values for MC
* MARS-2400: AMEX tech debt
* MARS-2507: Local date and time in DF12 should be in the same timezone for auth and reversals 

*Refined By:* Idea Refiner Script

*Dependencies:* To be determined

*Out of Scope:* To be determined