# Add support for Apple Pay transactions in our payment processing system

**Original Idea:** Add support for Apple Pay transactions in our payment processing system

*Business Value:*
Enhances system capabilities and operational efficiency. Add support for Apple Pay transactions in our payment processing system

*Acceptance Criteria:*
* Feature is implemented and tested
* Documentation is updated
* No regression in existing functionality

*Open Questions:*
* What are the performance requirements?
* Are there any compliance considerations?
* What is the rollback strategy?

*Suggested Design:*
Design should follow existing architectural patterns. Implement proper error handling and logging. Consider monitoring and alerting requirements. Plan for gradual rollout and feature flags.

*Impact and Notifications:*
Low to medium impact expected. Notify relevant teams before implementation. Plan for monitoring during rollout. Ensure rollback procedures are in place.

*Related Tickets:*

* MARS-2807: Set correct amount in recon events for partial reversals
* MARS-2804: remove or stop VTS windows VMs in our non-prod envs
* MARS-2781: test that we shouldn't be able to delete our node pools in staging
* MARS-2772: reject partial reversals for already captured transactions
* MARS-2770: use matched request as a cache for full_financial response processing

*Refined By:* Idea Refiner Script

*Dependencies:* To be determined

*Out of Scope:* To be determined