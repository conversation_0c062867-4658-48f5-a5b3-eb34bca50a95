#!/bin/bash

# Simple script to run the idea refiner with your data
# Make sure all required files exist before running

echo "Idea Refiner - Transform ideas into comprehensive JIRA tickets"
echo "============================================================="

# Check if required files exist
required_files=("jira_tickets.json" "enriched_tickets_clean.json" "tree.log" "gitlog.log")
missing_files=()

for file in "${required_files[@]}"; do
    if [[ ! -f "$file" ]]; then
        missing_files+=("$file")
    fi
done

if [[ ${#missing_files[@]} -gt 0 ]]; then
    echo "Error: Missing required files:"
    printf '  - %s\n' "${missing_files[@]}"
    echo ""
    echo "Please ensure you have:"
    echo "  - jira_tickets.json (historical JIRA tickets)"
    echo "  - enriched_tickets_clean.json (tickets with file changes)"
    echo "  - tree.log (project structure from 'tree' command)"
    echo "  - gitlog.log (git commit history from 'git log --name-status')"
    exit 1
fi

# Check for ideas file
ideas_file=""
if [[ -f "ideas.txt" ]]; then
    ideas_file="ideas.txt"
elif [[ -f "sample_ideas.txt" ]]; then
    ideas_file="sample_ideas.txt"
else
    echo "Error: No ideas file found."
    echo "Please create 'ideas.txt' with your ideas (one per line)"
    echo "Or use the provided 'sample_ideas.txt' for testing"
    exit 1
fi

echo "Using ideas from: $ideas_file"
echo "Output directory: refined_tickets/"
echo ""

# Run the idea refiner
echo "Running idea refiner..."
python3 idea_refiner.py \
    --ideas "$ideas_file" \
    --jira_tickets jira_tickets.json \
    --enriched_tickets enriched_tickets_clean.json \
    --tree_log tree.log \
    --git_log gitlog.log \
    --output_dir refined_tickets \
    --model qwen3 \
    --log_level INFO

if [[ $? -eq 0 ]]; then
    echo ""
    echo "✅ Success! Refined tickets generated in 'refined_tickets/' directory"
    echo ""
    echo "Generated files:"
    ls -la refined_tickets/*.txt | wc -l | xargs echo "  Total tickets:"
    echo ""
    echo "Each file contains a comprehensive ticket description that you can:"
    echo "  - Copy directly into JIRA"
    echo "  - Use as a template for refinement sessions"
    echo "  - Modify and enhance as needed"
else
    echo ""
    echo "❌ Error occurred during processing"
    echo "Check the error messages above for details"
    exit 1
fi
