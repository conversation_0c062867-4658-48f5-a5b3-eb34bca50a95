#!/usr/bin/env python3
"""
Test script for the idea refiner.
This script demonstrates how to use the idea refiner with the sample data.
"""

import subprocess
import sys
from pathlib import Path

def main():
    """Run the idea refiner with sample data."""
    
    # Check if sample_ideas.txt exists, if not use ideas.txt
    ideas_file = Path("sample_ideas.txt")
    if not ideas_file.exists():
        ideas_file = Path("ideas.txt")
        if not ideas_file.exists():
            print("Error: No ideas file found. Please create 'ideas.txt' or 'sample_ideas.txt'")
            sys.exit(1)
    
    # Required input files
    required_files = {
        "jira_tickets": "jira_tickets.json",
        "enriched_tickets": "enriched_tickets_clean.json", 
        "tree_log": "tree.log",
        "git_log": "gitlog.log"
    }
    
    # Check if all required files exist
    missing_files = []
    for name, filename in required_files.items():
        if not Path(filename).exists():
            missing_files.append(filename)
    
    if missing_files:
        print(f"Error: Missing required files: {', '.join(missing_files)}")
        sys.exit(1)
    
    # Run the idea refiner
    cmd = [
        "python3", "idea_refiner.py",
        "--ideas", str(ideas_file),
        "--jira_tickets", "jira_tickets.json",
        "--enriched_tickets", "enriched_tickets_clean.json",
        "--tree_log", "tree.log", 
        "--git_log", "gitlog.log",
        "--output_dir", "refined_tickets",
        "--model", "qwen3",  # You can change this to "gemma3" or "alma13"
        "--log_level", "INFO"
    ]
    
    print(f"Running: {' '.join(cmd)}")
    print(f"Using ideas from: {ideas_file}")
    print("This may take a few minutes depending on the number of ideas and model size...")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("Success!")
        print(result.stdout)
        if result.stderr:
            print("Warnings/Errors:")
            print(result.stderr)
            
        print("\nRefined tickets have been generated in the 'refined_tickets' directory.")
        print("Each idea has been converted into a comprehensive ticket description.")
        
    except subprocess.CalledProcessError as e:
        print(f"Error running idea refiner: {e}")
        print("STDOUT:", e.stdout)
        print("STDERR:", e.stderr)
        sys.exit(1)
    except FileNotFoundError:
        print("Error: idea_refiner.py not found in current directory")
        sys.exit(1)

if __name__ == "__main__":
    main()
